import React from 'react';
import AsyncImage from '@/components/ui/AsyncImage';
import { Deal } from '@/types/api';
import { getImageUrl } from '@/lib/utils';
import { useThemeClasses } from '@/config/theme';

interface DealCardProps {
  deal: Deal;
  onClick: () => void;
  className?: string;
}

const DealCard: React.FC<DealCardProps> = ({ deal, onClick, className }) => {
  const theme = useThemeClasses();

  return (
    <div
      className={`ghibli-card group overflow-visible cursor-pointer relative ${className} active:scale-95 transform transition-transform`}
      onClick={() => onClick && onClick()}
      style={{ aspectRatio: '1 / 1.2' }} // 稍微长方形
    >
      <div className="relative h-full flex flex-col">
        {/* Decorative background pattern */}
        <div className={`absolute inset-0 bg-gradient-to-r from-transparent via-secondary-50 to-accent-50 opacity-50 rounded-lg`}></div>

        {/* Floating decorative elements */}
        <div className="absolute top-2 left-2 w-2 h-2 mobile:w-3 mobile:h-3 bg-secondary-200 rounded-full opacity-40 float-animation" style={{animationDelay: '1s'}}></div>

        {/* Image section - 正方形，占据卡片宽度 */}
        <div className="relative w-full aspect-square flex-shrink-0">
          {deal.img ? (
            <AsyncImage
              src={getImageUrl(deal.img)}
              alt={deal.title || 'Deal'}
              className="w-full h-full object-cover rounded-t-lg"
              fallback="/images/deal-placeholder.svg"
              placeholder={
                <div className={`w-full h-full bg-gradient-to-br from-secondary-100 to-accent-100 rounded-t-lg flex items-center justify-center`}>
                  <span className="text-secondary-400 text-3xl">🎁</span>
                </div>
              }
            />
          ) : (
            <div className={`w-full h-full bg-gradient-to-br from-secondary-100 to-accent-100 rounded-t-lg flex items-center justify-center`}>
              <span className="text-secondary-400 text-3xl">🎁</span>
            </div>
          )}

          {/* Discount tag - 右上角，部分超出卡片 */}
          {deal.discount && (
            <div className="absolute -top-3 -right-3 z-20">
              <span className={`inline-block ${theme.dealCard.discountBadge.background} ${theme.dealCard.discountBadge.text} px-3 py-2 rounded-full text-xs font-bold shadow-lg transform rotate-12 border-2 border-white`}>
                {deal.discount}
              </span>
            </div>
          )}

          {/* Hover overlay with Get Deal button - 移动端优化 */}
          <div className="absolute inset-0 bg-black bg-opacity-60 opacity-0 group-hover:opacity-100 mobile:opacity-0 transition-all duration-300 rounded-t-lg flex items-center justify-center">
            <button
              onClick={(e) => {
                e.stopPropagation();
                if (onClick) onClick();
              }}
              className={`${theme.dealCard.button.background} ${theme.dealCard.button.text} ${theme.dealCard.button.hover} px-mobile-4 md:px-6 py-mobile-2 md:py-3 rounded-lg font-semibold transition-all duration-300 transform translate-y-2 group-hover:translate-y-0 opacity-0 group-hover:opacity-100 shadow-xl hover:scale-105 text-mobile-sm md:text-base min-h-touch`}
            >
              Get Deal
            </button>
          </div>
        </div>

        {/* Title section - 在图片下方 */}
        <div className="p-mobile-2 md:p-3 flex-1 flex items-center">
          <h3 className={`text-mobile-sm md:text-sm font-bold text-neutral-800 group-hover:text-secondary-600 transition-colors line-clamp-2 text-center w-full deal-title`} data-from-api>
            {deal.title || 'Special Deal'}
          </h3>
        </div>
      </div>
    </div>
  );
};

export default DealCard;
