import { getConfig } from './index';

// 配置访问工具函数
export const useConfig = () => {
  return getConfig();
};

// 获取站点配置
export const useSiteConfig = () => {
  return getConfig().siteConfig;
};

// 获取SEO配置
export const useSeoConfig = () => {
  return getConfig().seoConfig;
};

// 获取内容配置
export const useContentConfig = () => {
  return getConfig().content;
};

// 获取品牌配置
export const useBrandingConfig = () => {
  return getConfig().branding;
};

// 获取导航配置
export const useNavigationConfig = () => {
  return getConfig().navigation;
};

// 获取语言配置
export const useLanguageConfig = () => {
  return getConfig().languages;
};

// 获取广告配置
export const useAdConfig = () => {
  return {
    adUnits: getConfig().adUnits,
    modalAdTopProbability: getConfig().modalAdTopProbability
  };
};

// 获取注入配置
export const useInjectionConfig = () => {
  return {
    headList: getConfig().headList,
    bodyList: getConfig().bodyList
  };
};

// 获取页面内容配置
export const usePageConfig = (pageName: 'about' | 'contact' | 'privacy' | 'terms') => {
  return getConfig().content.staticPages[pageName];
};

// 获取页脚配置
export const useFooterConfig = () => {
  return getConfig().content.footer;
};

// 获取全局变量
export const useSiteVariables = () => {
  const config = getConfig();
  return {
    siteName: config.siteConfig.name,
    siteUrl: config.siteConfig.url,
    contactEmail: config.siteConfig.contact.email,
    contactPhone: config.siteConfig.contact.phone,
    contactAddress: config.siteConfig.contact.address
  };
};
