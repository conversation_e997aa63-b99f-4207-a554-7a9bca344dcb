import { NavigationItem, LanguageOption } from '../types';

export const navigation: NavigationItem[] = [
  { name: 'Home', href: '/' },
  { name: 'Coupons', href: '/coupons' },
  { name: 'Deals', href: '/deals' },
  { name: 'Brands', href: '/brands' },
  { name: 'Categories', href: '/categories' },
  { name: 'Blog', href: '/blog' }
];

export const languages: LanguageOption[] = [
  { code: 'en', name: 'English', flag: '🇺🇸' },
  { code: 'es', name: 'Español', flag: '🇪🇸' },
  { code: 'fr', name: 'Français', flag: '🇫🇷' }
];
