import { ContentConfig } from '../types';
import { aboutPageContent, contactPageContent, privacyPageContent, termsPageContent } from './pages';
import { footerConfig } from './footer';

export const content: ContentConfig = {
  hero: {
    title: 'Save More with Every Purchase',
    subtitle: 'Turn every purchase into a saving opportunity',
    searchPlaceholder: 'Find the perfect deal for you...'
  },
  sections: {
    categories: 'Explore Categories',
    brands: 'Popular Brands',
    featuredCoupons: 'Featured Coupons',
    featuredDeals: 'Hot Deals 🔥'
  },
  buttons: {
    search: 'Search Deals ✨',
    viewAll: 'View All',
    getCoupon: 'Get Coupon',
    getDeal: 'Get Deal',
    subscribe: 'Subscribe ✨',
    unsubscribe: 'Unsubscribe'
  },
  messages: {
    loading: 'Loading amazing deals...',
    noResults: 'No deals found. Try a different search!',
    error: 'Oops! Something went wrong. Please try again.',
    success: 'Success! Your savings journey continues.'
  },
  pageContent: {
    brands: {
      title: 'Popular Brands 🏪',
      subtitle: 'Discover amazing brands and their exclusive offers from around the world',
      filterTitle: 'Find Your Favorite Brand',
      noResultsTitle: 'No Brands Found',
      noResultsMessage: 'Try adjusting your search or category filter to discover more brands.'
    },
    coupons: {
      title: 'Exclusive Coupons',
      subtitle: 'Unlock incredible savings with our handpicked collection of verified coupon codes',
      filterTitle: 'Find Your Perfect Coupon',
      noResultsTitle: 'No Coupons Found',
      noResultsMessage: 'Try adjusting your search or filter criteria'
    },
    deals: {
      title: 'Hot Deals 🔥',
      subtitle: 'Discover the best deals and limited-time offers from top brands',
      filterTitle: 'Find Amazing Deals',
      noResultsTitle: 'No Deals Found',
      noResultsMessage: 'Try adjusting your search or filter criteria'
    },
    categories: {
      title: 'Browse Categories',
      subtitle: 'Explore deals organized by category to find exactly what you need',
      noResultsTitle: 'No categories found',
      noResultsMessage: 'Please try again later'
    },
    blog: {
      title: 'Blog & Insights',
      subtitle: 'Discover the latest deals, shopping tips, and brand insights to maximize your savings',
      noResultsTitle: 'No articles found',
      noResultsMessage: 'Check back later for new content!',
      backToBlog: 'Back to Blog',
      articleNotFound: 'Article Not Found',
      articleNotFoundMessage: 'The article you are looking for does not exist.',
      showingResults: 'Showing {start} to {end} of {total} articles'
    },
    search: {
      title: 'Search Results',
      subtitle: 'Find the best deals and coupons for your favorite brands',
      placeholder: 'Search for brands, deals, or coupons...',
      noResultsTitle: 'No Results Found',
      noResultsMessage: 'Try different keywords or browse our categories'
    }
  },
  newsletter: {
    title: 'Subscribe for Amazing Deals! 🔥',
    description: 'Subscribe to our newsletter and be the first to know about exclusive coupons, flash sales, and magical savings opportunities.',
    placeholder: 'Enter your email for exclusive deals...',
    benefits: [
      'Exclusive deals',
      'Early access',
      'No spam, ever'
    ],
    privacy: 'We respect your privacy. Unsubscribe at any time.',
    successTitle: '🎉 You\'re All Set!',
    successMessage: 'Thank you for subscribing! Get ready for amazing deals delivered straight to your inbox.'
  },
  footer: footerConfig,
  staticPages: {
    about: aboutPageContent,
    contact: contactPageContent,
    privacy: privacyPageContent,
    terms: termsPageContent
  }
};
