// 全局注入配置 - 用于在构建时注入到HTML中的内容
export interface InjectionConfig {
  headList: string[];
  bodyList: string[];
}

// 需要注入到每个页面 <head> 中的内容
export const headList: string[] = [
  '<script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-1347940398171208" crossorigin="anonymous"></script>',
  '<meta name="ppck-ver" content="9468d15cf807b1666dcc82d42bb92202" />',
];

// 需要注入到每个页面 </body> 前面的内容
export const bodyList: string[] = [
  // 可以在这里添加需要在body底部加载的脚本
  // 例如：Google Analytics、其他追踪代码等
  '<!-- Body injection test -->',
];

// 导出完整的注入配置
export const injectionConfig: InjectionConfig = {
  headList,
  bodyList,
};
