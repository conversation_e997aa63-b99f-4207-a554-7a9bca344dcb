import { SeoConfig, SiteConfig } from '../types';
import { SITE_VARIABLES } from './variables';

export const seoConfig: SeoConfig = {
  title: `${SITE_VARIABLES.SITE_NAME} - Best Deals & Discount Coupons`,
  description: SITE_VARIABLES.SHORT_DESCRIPTION,
  keywords: ['coupons', 'deals', 'discounts', 'promo codes', 'savings', 'offers'],
  ogImage: '/assets/og-image.jpg',
  siteName: SITE_VARIABLES.SITE_NAME,
  siteUrl: SITE_VARIABLES.SITE_URL,
  // twitterHandle: SITE_VARIABLES.TWITTER_HANDLE, // 可选 - 如果需要可以取消注释
  // facebookAppId: SITE_VARIABLES.FACEBOOK_APP_ID // 可选 - 如果需要可以取消注释
};

export const siteConfig: SiteConfig = {
  name: SITE_VARIABLES.SITE_NAME,
  description: SITE_VARIABLES.TAGLINE,
  url: SITE_VARIABLES.SITE_URL,
  defaultLanguage: 'en',
  supportedLanguages: ['en', 'es', 'fr'],
  apiBaseUrl: SITE_VARIABLES.ENVIRONMENT.IS_PRODUCTION ? SITE_VARIABLES.ENVIRONMENT.API_BASE_URL_PRODUCTION : SITE_VARIABLES.ENVIRONMENT.API_BASE_URL_DEVELOPMENT,
  ogImage: `${SITE_VARIABLES.SITE_URL}/og-image.jpg`,
  // links: { // 可选 - 如果需要社交媒体链接可以取消注释
  //   twitter: SITE_VARIABLES.TWITTER_URL,
  //   facebook: SITE_VARIABLES.FACEBOOK_URL,
  //   instagram: SITE_VARIABLES.INSTAGRAM_URL
  // },
  contact: {
    email: SITE_VARIABLES.CONTACT_EMAIL,
    // phone: SITE_VARIABLES.CONTACT_PHONE, // 可选
    // address: SITE_VARIABLES.CONTACT_ADDRESS // 可选
  }
};
