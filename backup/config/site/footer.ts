import { FooterConfig } from '../types';
import { SITE_VARIABLES, COMBINED_VARIABLES } from './variables';

export const footerConfig: FooterConfig = {
  description: SITE_VARIABLES.TAGLINE,
  copyright: COMBINED_VARIABLES.COPYRIGHT,
  showCategories: true,
  showBrands: true,
  statistics: {
    brands: 16000,
    coupons: 50000,
    deals: 25000
  },
  newsletter: {
    title: 'Stay Updated with Latest Deals',
    description: 'Get exclusive coupons and deals delivered straight to your inbox. Never miss a saving opportunity!',
    placeholder: 'Enter your email address',
    buttonText: 'Subscribe'
  },
  trustBadges: {
    worldwide: '🌍 Available Worldwide',
    secure: '🔒 Secure & Trusted',
    updated: '⚡ Updated Daily'
  },
  sections: {
    categories: 'Popular Categories',
    brands: 'Featured Brands'
  },
  links: {
    company: {
      title: 'Company',
      items: [
        { name: 'About Us', href: '/about' },
        { name: 'Contact', href: '/contact' }
      ]
    },

    legal: {
      title: 'Legal',
      items: [
        { name: 'Privacy Policy', href: '/privacy' },
        { name: 'Terms of Service', href: '/terms' },
        { name: 'Cookie Policy', href: '/cookies' }
      ]
    }
  }
  // social 部分是可选的，可以完全移除
  // 如果需要社交媒体链接，可以取消注释下面的代码：
  /*
  social: {
    title: 'Follow Us',
    items: [
      {
        name: 'Twitter',
        href: SITE_VARIABLES.TWITTER_URL,
        icon: '🐦'
      },
      {
        name: 'Facebook',
        href: SITE_VARIABLES.FACEBOOK_URL,
        icon: '📘'
      },
      {
        name: 'Instagram',
        href: SITE_VARIABLES.INSTAGRAM_URL,
        icon: '📷'
      }
    ]
  }
  */
};
