import { Config } from './types';
import { adUnits, adConfig } from './ads/units';
import { injectionConfig } from './site/injection';
import { seoConfig, siteConfig } from './site/metadata';
import { navigation, languages } from './site/navigation';
import { branding } from './site/branding';
import { content } from './site/content';

// Configuration based on environment
const getEnvironmentConfig = (): Config => {
  const baseConfig: Config = {
    modalAdTopProbability: adConfig.modalAdTopProbability,
    adUnits,
    headList: injectionConfig.headList,
    bodyList: injectionConfig.bodyList,
    seoConfig,
    siteConfig,
    navigation,
    languages,
    branding,
    content
  };

  return baseConfig;
};

let config: Config | null = null;

export function getConfig(): Config {
  if (!config) {
    config = getEnvironmentConfig();
  }
  return config;
}
