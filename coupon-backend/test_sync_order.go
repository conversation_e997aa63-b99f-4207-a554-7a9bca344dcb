package main

import (
	"fmt"

	"coupon-backend/infra/constant"
	"coupon-backend/infra/external_gateway/fatcouponlib"
	"coupon-backend/infra/external_gateway/linkbuxlib"
)

func main() {
	fmt.Println("Testing Sync Order and API Connectivity")
	fmt.Println("=======================================")

	// Process each account in the same order as the sync task
	for i, accountConfig := range constant.MerchantsAccountList {
		platformType := accountConfig["type"].(string)
		priority := accountConfig["priority"].(int)
		accountName := accountConfig["account_name"].(string)

		fmt.Printf("\n%d. Processing account: %s (platform: %s, priority: %d)\n", 
			i+1, accountName, platformType, priority)

		// Test API connectivity with a small limit
		testConfig := make(map[string]interface{})
		for k, v := range accountConfig {
			testConfig[k] = v
		}
		testConfig["limit"] = 5 // Small limit for testing

		var externalData []map[string]interface{}
		var apiErr error

		switch platformType {
		case constant.AccountTypeLinkbux:
			fmt.Printf("   Testing Linkbux API...")
			externalData, apiErr = linkbuxlib.BatchGetMerchants(accountName, testConfig)
		case constant.AccountTypeFatcoupon:
			fmt.Printf("   Testing FatCoupon API...")
			externalData, apiErr = fatcouponlib.BatchGetMerchants(accountName, testConfig)
		default:
			fmt.Printf("   Unsupported platform type: %s\n", platformType)
			continue
		}

		if apiErr != nil {
			fmt.Printf(" ❌ FAILED: %v\n", apiErr)
		} else {
			fmt.Printf(" ✅ SUCCESS: Got %d merchants\n", len(externalData))
			if len(externalData) > 0 {
				merchant := externalData[0]
				fmt.Printf("      Sample merchant: %s (%s)\n", 
					merchant["name"], merchant["site_url"])
			}
		}
	}

	fmt.Println("\n=======================================")
	fmt.Println("Analysis:")
	fmt.Println("- If Linkbux fails, no Linkbux merchants will be saved")
	fmt.Println("- If FatCoupon succeeds, FatCoupon merchants will be saved")
	fmt.Println("- Deduplication happens AFTER all platforms are processed")
	fmt.Println("- Only merchants with same site_url are deduplicated")
}
