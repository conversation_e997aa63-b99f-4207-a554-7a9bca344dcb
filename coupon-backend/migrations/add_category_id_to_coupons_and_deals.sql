-- 为coupons表添加category_id字段
ALTER TABLE coupons ADD COLUMN category_id INTEGER NOT NULL DEFAULT 1;

-- 为deals表添加category_id字段  
ALTER TABLE deals ADD COLUMN category_id INTEGER NOT NULL DEFAULT 1;

-- 添加外键约束（可选，如果需要的话）
-- ALTER TABLE coupons ADD CONSTRAINT fk_coupons_category_id FOREIGN KEY (category_id) REFERENCES categories(id);
-- ALTER TABLE deals ADD CONSTRAINT fk_deals_category_id FOREIGN KEY (category_id) REFERENCES categories(id);

-- 添加索引以提高查询性能
CREATE INDEX idx_coupons_category_id ON coupons(category_id);
CREATE INDEX idx_deals_category_id ON deals(category_id);

-- 添加复合索引以支持分类+状态的查询
CREATE INDEX idx_coupons_category_status ON coupons(category_id, status);
CREATE INDEX idx_deals_category_status ON deals(category_id, status);
