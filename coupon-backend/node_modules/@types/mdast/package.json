{"name": "@types/mdast", "version": "4.0.4", "description": "TypeScript definitions for mdast", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/mdast", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/ChristianMurphy"}, {"name": "<PERSON>", "githubUsername": "lujun2", "url": "https://github.com/lujun2"}, {"name": "Re<PERSON><PERSON>", "githubUsername": "rem<PERSON><PERSON><PERSON>", "url": "https://github.com/remcohaszing"}, {"name": "<PERSON>", "githubUsername": "wooorm", "url": "https://github.com/wooorm"}, {"name": "Re<PERSON><PERSON>", "githubUsername": "rem<PERSON><PERSON><PERSON>", "url": "https://github.com/remcohaszing"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/mdast"}, "scripts": {}, "dependencies": {"@types/unist": "*"}, "typesPublisherContentHash": "1599d3ca45533e9d9248231c90843306b49c07fe13ad94ebf7345da44d8fd4bd", "typeScriptVersion": "4.7"}