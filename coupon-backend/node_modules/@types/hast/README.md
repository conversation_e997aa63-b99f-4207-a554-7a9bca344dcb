# Installation
> `npm install --save @types/hast`

# Summary
This package contains type definitions for hast (https://github.com/syntax-tree/hast).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/hast.

### Additional Details
 * Last updated: <PERSON><PERSON>, 30 Jan 2024 21:35:45 GMT
 * Dependencies: [@types/unist](https://npmjs.com/package/@types/unist)

# Credits
These definitions were written by [lukeggchapman](https://github.com/lukeggchapman), [<PERSON><PERSON><PERSON>](https://github.com/rokt33r), [<PERSON>](https://github.com/ChristianMurphy), and [<PERSON><PERSON><PERSON>](https://github.com/remcohaszing).
