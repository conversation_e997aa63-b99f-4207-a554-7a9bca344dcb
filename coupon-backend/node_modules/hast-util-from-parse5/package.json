{"author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "bugs": "https://github.com/syntax-tree/hast-util-from-parse5/issues", "contributors": ["<PERSON> <<EMAIL>> (https://wooorm.com)"], "dependencies": {"@types/hast": "^3.0.0", "@types/unist": "^3.0.0", "devlop": "^1.0.0", "hastscript": "^9.0.0", "property-information": "^7.0.0", "vfile": "^6.0.0", "vfile-location": "^5.0.0", "web-namespaces": "^2.0.0"}, "description": "hast utility to transform from a `parse5` AST", "devDependencies": {"@types/node": "^22.0.0", "c8": "^10.0.0", "is-hidden": "^2.0.0", "parse5": "^7.0.0", "prettier": "^3.0.0", "remark-cli": "^12.0.0", "remark-preset-wooorm": "^11.0.0", "to-vfile": "^8.0.0", "type-coverage": "^2.0.0", "typescript": "^5.0.0", "unist-util-visit": "^5.0.0", "xo": "^0.60.0"}, "exports": "./index.js", "files": ["index.d.ts", "index.js", "lib/"], "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}, "keywords": ["ast", "change", "hast-util", "hast", "transform", "unist", "utility", "util"], "license": "MIT", "name": "hast-util-from-parse5", "prettier": {"bracketSpacing": false, "semi": false, "singleQuote": true, "tabWidth": 2, "trailingComma": "none", "useTabs": false}, "remarkConfig": {"plugins": ["remark-preset-wooorm"]}, "repository": "syntax-tree/hast-util-from-parse5", "scripts": {"build": "tsc --build --clean && tsc --build && type-coverage", "format": "remark --frail --quiet --output -- . && prettier --log-level warn --write -- . && xo --fix", "test-api": "node --conditions development test/index.js", "test-coverage": "c8 --100 --reporter lcov -- npm run test-api", "test": "npm run build && npm run format && npm run test-coverage"}, "sideEffects": false, "typeCoverage": {"atLeast": 100, "strict": true}, "type": "module", "version": "8.0.3", "xo": {"overrides": [{"files": ["**/*.d.ts"], "rules": {"@typescript-eslint/array-type": ["error", {"default": "generic"}], "@typescript-eslint/ban-types": ["error", {"extendDefaults": true}], "@typescript-eslint/consistent-type-definitions": ["error", "interface"]}}, {"files": "test/**/*.js", "rules": {"no-await-in-loop": "off"}}], "prettier": true, "rules": {"max-depth": "off", "unicorn/prefer-at": "off"}}}