!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t(e.dequal={})}(this,(function(e){var t=Object.prototype.hasOwnProperty;function r(e,t,r){for(r of e.keys())if(n(r,t))return r}function n(e,f){var i,o,u;if(e===f)return!0;if(e&&f&&(i=e.constructor)===f.constructor){if(i===Date)return e.getTime()===f.getTime();if(i===RegExp)return e.toString()===f.toString();if(i===Array){if((o=e.length)===f.length)for(;o--&&n(e[o],f[o]););return-1===o}if(i===Set){if(e.size!==f.size)return!1;for(o of e){if((u=o)&&"object"==typeof u&&!(u=r(f,u)))return!1;if(!f.has(u))return!1}return!0}if(i===Map){if(e.size!==f.size)return!1;for(o of e){if((u=o[0])&&"object"==typeof u&&!(u=r(f,u)))return!1;if(!n(o[1],f.get(u)))return!1}return!0}if(i===ArrayBuffer)e=new Uint8Array(e),f=new Uint8Array(f);else if(i===DataView){if((o=e.byteLength)===f.byteLength)for(;o--&&e.getInt8(o)===f.getInt8(o););return-1===o}if(ArrayBuffer.isView(e)){if((o=e.byteLength)===f.byteLength)for(;o--&&e[o]===f[o];);return-1===o}if(!i||"object"==typeof e){for(i in o=0,e){if(t.call(e,i)&&++o&&!t.call(f,i))return!1;if(!(i in f)||!n(e[i],f[i]))return!1}return Object.keys(f).length===o}}return e!=e&&f!=f}e.dequal=n}));