package scheduler

import (
	"context"
	"coupon-backend/domain/task/service"
	"log"
	"time"

	"github.com/go-co-op/gocron/v2"
)

// Scheduler task scheduler
type Scheduler struct {
	taskService service.TaskService
	scheduler   gocron.Scheduler
	ctx         context.Context
	cancel      context.CancelFunc
}

// NewScheduler creates a new scheduler
func NewScheduler(taskService service.TaskService) *Scheduler {
	ctx, cancel := context.WithCancel(context.Background())
	scheduler, err := gocron.NewScheduler()
	if err != nil {
		log.Fatalf("Failed to create scheduler: %v", err)
	}

	return &Scheduler{
		taskService: taskService,
		scheduler:   scheduler,
		ctx:         ctx,
		cancel:      cancel,
	}
}

// Start starts the scheduler
func (s *Scheduler) Start() {
	log.Println("Starting task scheduler...")

	// Add scheduled jobs
	s.addScheduledJobs()

	// Start scheduler
	s.scheduler.Start()

	// Wait for stop signal
	<-s.ctx.Done()
	log.Println("Task scheduler stopped")
}

// Stop stops the scheduler
func (s *Scheduler) Stop() {
	s.cancel()
	if err := s.scheduler.Shutdown(); err != nil {
		log.Printf("Error shutting down scheduler: %v", err)
	}
}

// addScheduledJobs adds all scheduled jobs
func (s *Scheduler) addScheduledJobs() {
	// Brand sync task - execute daily at 2:00 AM
	_, err := s.scheduler.NewJob(
		gocron.CronJob("0 2 * * *", false),
		gocron.NewTask(s.executeBrandSync),
	)
	if err != nil {
		log.Printf("Failed to add brand sync job: %v", err)
	}

	// Coupon sync task - execute every 4 hours
	_, err = s.scheduler.NewJob(
		gocron.DurationJob(4*time.Hour),
		gocron.NewTask(s.executeCouponSync),
	)
	if err != nil {
		log.Printf("Failed to add coupon sync job: %v", err)
	}

	// Deal sync task - execute every 6 hours
	_, err = s.scheduler.NewJob(
		gocron.DurationJob(6*time.Hour),
		gocron.NewTask(s.executeDealSync),
	)
	if err != nil {
		log.Printf("Failed to add deal sync job: %v", err)
	}

	// Cache warmup task - execute every 30 minutes
	_, err = s.scheduler.NewJob(
		gocron.DurationJob(30*time.Minute),
		gocron.NewTask(s.executeCacheWarmup),
	)
	if err != nil {
		log.Printf("Failed to add cache warmup job: %v", err)
	}

	log.Println("All scheduled jobs added successfully")

	// Execute initial sync sequence: brands → coupons → deals
	go s.executeInitialSyncSequence()
}

// executeInitialSyncSequence executes the initial sync sequence in order
func (s *Scheduler) executeInitialSyncSequence() {
	log.Println("🚀 Starting initial sync sequence...")

	// Wait a bit for scheduler to fully start
	time.Sleep(2 * time.Second)

	// Step 1: Sync brands first
	log.Println("📋 Step 1: Executing brand sync...")
	if err := s.taskService.SyncBrands(); err != nil {
		log.Printf("❌ Initial brand sync failed: %v", err)
		return
	}
	log.Println("✅ Step 1: Brand sync completed")

	// Step 2: Sync coupons (depends on brands)
	log.Println("🎫 Step 2: Executing coupon sync...")
	if err := s.taskService.SyncCoupons(); err != nil {
		log.Printf("❌ Initial coupon sync failed: %v", err)
		return
	}
	log.Println("✅ Step 2: Coupon sync completed")

	// Step 3: Sync deals (depends on brands)
	log.Println("💰 Step 3: Executing deal sync...")
	if err := s.taskService.SyncDeals(); err != nil {
		log.Printf("❌ Initial deal sync failed: %v", err)
		return
	}
	log.Println("✅ Step 3: Deal sync completed")

	log.Println("🎉 Initial sync sequence completed successfully!")
}

// executeBrandSync executes brand sync
func (s *Scheduler) executeBrandSync() {
	log.Println("Executing brand sync task...")
	if err := s.taskService.SyncBrands(); err != nil {
		log.Printf("Brand sync task failed: %v", err)
	}
}

// executeCouponSync executes coupon sync
func (s *Scheduler) executeCouponSync() {
	log.Println("Executing coupon sync task...")
	if err := s.taskService.SyncCoupons(); err != nil {
		log.Printf("Coupon sync task failed: %v", err)
	}
}

// executeDealSync executes deal sync
func (s *Scheduler) executeDealSync() {
	log.Println("Executing deal sync task...")
	if err := s.taskService.SyncDeals(); err != nil {
		log.Printf("Deal sync task failed: %v", err)
	}
}

// executeCacheWarmup executes cache warmup
func (s *Scheduler) executeCacheWarmup() {
	log.Println("Executing cache warmup task...")
	if err := s.taskService.WarmupCache(); err != nil {
		log.Printf("Cache warmup task failed: %v", err)
	}
}
