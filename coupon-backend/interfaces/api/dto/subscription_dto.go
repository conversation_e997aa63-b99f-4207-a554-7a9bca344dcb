package dto

import "time"

// SubscribeReq 订阅请求
type SubscribeReq struct {
	Email     string `json:"email" binding:"required,email" example:"<EMAIL>"`
	Source    string `json:"source,omitempty" example:"website"`
	IPAddress string `json:"-"` // 从请求中获取，不需要用户传递
	UserAgent string `json:"-"` // 从请求中获取，不需要用户传递
}

// SubscribeResp 订阅响应
type SubscribeResp struct {
	Message string `json:"message" example:"Successfully subscribed"`
}

// UnsubscribeReq 取消订阅请求
type UnsubscribeReq struct {
	Email string `json:"email" binding:"required,email" example:"<EMAIL>"`
}

// UnsubscribeResp 取消订阅响应
type UnsubscribeResp struct {
	Message string `json:"message" example:"Successfully unsubscribed"`
}

// SubscriptionListReq 订阅列表请求
type SubscriptionListReq struct {
	Page     int    `form:"page" example:"1"`
	PageSize int    `form:"page_size" example:"20"`
	Status   *int   `form:"status" example:"1"`
	Email    string `form:"email" example:"<EMAIL>"`
	Source   string `form:"source" example:"website"`
}

// SubscriptionItem 订阅项
type SubscriptionItem struct {
	ID        int       `json:"id" example:"1"`
	Email     string    `json:"email" example:"<EMAIL>"`
	Status    int       `json:"status" example:"1"`
	Source    string    `json:"source" example:"website"`
	CreatedAt time.Time `json:"created_at" example:"2023-01-01T00:00:00Z"`
	UpdatedAt time.Time `json:"updated_at" example:"2023-01-01T00:00:00Z"`
}

// SubscriptionListResp 订阅列表响应
type SubscriptionListResp struct {
	Items    []*SubscriptionItem `json:"items"`
	Total    int64               `json:"total" example:"100"`
	Page     int                 `json:"page" example:"1"`
	PageSize int                 `json:"page_size" example:"20"`
}
