package router

import (
	"coupon-backend/application/article/dto"
	brandAppService "coupon-backend/application/brand/appservice"
	categoryAppService "coupon-backend/application/category/appservice"
	couponAppService "coupon-backend/application/coupon/appservice"
	dealAppService "coupon-backend/application/deal/appservice"
	searchAppService "coupon-backend/application/search/appservice"
	"coupon-backend/application/subscription"
	"coupon-backend/infra/cache"
	"coupon-backend/infra/ecode"
	"coupon-backend/infra/response"
	articleHandler "coupon-backend/interfaces/api/handler/article"
	brandHandler "coupon-backend/interfaces/api/handler/brand"
	categoryHandler "coupon-backend/interfaces/api/handler/category"
	couponHandler "coupon-backend/interfaces/api/handler/coupon"
	dealHandler "coupon-backend/interfaces/api/handler/deal"
	searchHandler "coupon-backend/interfaces/api/handler/search"
	sitemapHandler "coupon-backend/interfaces/api/handler/sitemap"
	subscriptionHandler "coupon-backend/interfaces/api/handler/subscription"
	"coupon-backend/interfaces/api/middleware"
	"net/http"

	"github.com/gin-gonic/gin"
)

// RouterConfig 路由配置
type RouterConfig struct {
	ArticleAppService      any
	BrandAppService        brandAppService.BrandAppService
	CategoryAppService     categoryAppService.CategoryAppService
	CouponAppService       couponAppService.CouponAppService
	DealAppService         dealAppService.DealAppService
	SearchAppService       searchAppService.SearchAppService
	SubscriptionAppService *subscription.SubscriptionApp
	CacheService           cache.CacheService
}

// SetupRouter 设置路由
func SetupRouter(config *RouterConfig) *gin.Engine {
	router := gin.New()

	// 添加性能优化中间件
	router.Use(gin.Recovery())
	router.Use(middleware.CORS())
	router.Use(middleware.PerformanceMiddleware())
	router.Use(middleware.CompressionMiddleware())
	router.Use(middleware.CacheControlMiddleware())
	router.Use(middleware.RequestSizeLimit(10 << 20)) // 10MB限制

	// 健康检查
	router.GET("/health", func(c *gin.Context) {
		response.Success(c, gin.H{
			"status":  "ok",
			"message": "service is running",
		})
	})

	// API v1 路由组
	v1 := router.Group("/api/v1")
	{
		// 创建handlers
		articleH := articleHandler.NewArticleHandler(config.ArticleAppService.(interface {
			GetArticleDetailById(ctx *gin.Context, id uint64) (*dto.ArticleDetailResp, *ecode.Error)
			GetArticleDetailBySlug(ctx *gin.Context, slug string) (*dto.ArticleDetailResp, *ecode.Error)
			GetArticleList(ctx *gin.Context, req *dto.GetArticleListReq) (*dto.ArticleListResp, *ecode.Error)
			GetArticleCount(ctx *gin.Context) (int64, *ecode.Error)
		}))
		categoryH := categoryHandler.NewCategoryHandler(config.CategoryAppService)
		brandH := brandHandler.NewBrandHandler(config.BrandAppService)
		couponH := couponHandler.NewCouponHandler(config.CouponAppService)
		dealH := dealHandler.NewDealHandler(config.DealAppService)
		searchH := searchHandler.NewSearchHandler(config.SearchAppService)
		sitemapH := sitemapHandler.NewSitemapHandler(config.BrandAppService, config.CategoryAppService, config.CouponAppService, config.DealAppService, config.CacheService)
		subscriptionH := subscriptionHandler.NewSubscriptionHandler(config.SubscriptionAppService)

		// 注册路由
		articleH.RegisterRoutes(v1)
		categoryH.RegisterRoutes(v1)
		brandH.RegisterRoutes(v1)
		couponH.RegisterRoutes(v1)
		dealH.RegisterRoutes(v1)
		searchH.RegisterRoutes(v1)
		sitemapH.RegisterRoutes(v1)

		// 订阅路由
		v1.POST("/subscribe", subscriptionH.Subscribe)
		v1.POST("/unsubscribe", subscriptionH.Unsubscribe)
		v1.GET("/subscriptions", subscriptionH.GetSubscriptions)
	}

	// 404处理
	router.NoRoute(func(c *gin.Context) {
		c.JSON(http.StatusNotFound, gin.H{
			"code":    http.StatusNotFound,
			"message": "route not found",
		})
	})

	return router
}
