package middleware

import (
	"log"
	"time"

	"github.com/gin-gonic/gin"
)

// PerformanceMiddleware 性能监控中间件
func PerformanceMiddleware() gin.HandlerFunc {
	return gin.HandlerFunc(func(c *gin.Context) {
		start := time.Now()

		// 处理请求
		c.Next()

		// 计算响应时间
		duration := time.Since(start)

		// 记录慢查询（超过100ms的请求）
		if duration > 100*time.Millisecond {
			log.Printf("SLOW REQUEST: %s %s took %v", 
				c.Request.Method, 
				c.Request.URL.Path, 
				duration)
		}

		// 添加响应时间头（用于调试）
		c.Header("X-Response-Time", duration.String())
	})
}

// RequestSizeLimit 请求大小限制中间件
func RequestSizeLimit(maxSize int64) gin.HandlerFunc {
	return gin.HandlerFunc(func(c *gin.Context) {
		if c.Request.ContentLength > maxSize {
			c.<PERSON>(413, gin.H{"error": "Request entity too large"})
			c.Abort()
			return
		}
		c.Next()
	})
}

// CacheControlMiddleware 缓存控制中间件
func CacheControlMiddleware() gin.HandlerFunc {
	return gin.HandlerFunc(func(c *gin.Context) {
		// 对于GET请求，设置缓存头
		if c.Request.Method == "GET" {
			// 根据路径设置不同的缓存策略
			path := c.Request.URL.Path
			
			if isStaticResource(path) {
				// 静态资源长时间缓存
				c.Header("Cache-Control", "public, max-age=86400") // 24小时
			} else if isAPIEndpoint(path) {
				// API端点短时间缓存
				c.Header("Cache-Control", "public, max-age=60") // 1分钟
			}
		}
		
		c.Next()
	})
}

// isStaticResource 判断是否为静态资源
func isStaticResource(path string) bool {
	staticPaths := []string{"/static/", "/assets/", "/images/", "/css/", "/js/"}
	for _, staticPath := range staticPaths {
		if len(path) >= len(staticPath) && path[:len(staticPath)] == staticPath {
			return true
		}
	}
	return false
}

// isAPIEndpoint 判断是否为API端点
func isAPIEndpoint(path string) bool {
	return len(path) >= 4 && path[:4] == "/api"
}
