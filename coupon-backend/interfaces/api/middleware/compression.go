package middleware

import (
	"compress/gzip"
	"io"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
)

// CompressionMiddleware 响应压缩中间件
func CompressionMiddleware() gin.HandlerFunc {
	return gin.HandlerFunc(func(c *gin.Context) {
		// 检查客户端是否支持gzip压缩
		if !shouldCompress(c.Request) {
			c.Next()
			return
		}

		// 设置压缩响应头
		c.Header("Content-Encoding", "gzip")
		c.Header("Vary", "Accept-Encoding")

		// 创建gzip writer
		gz := gzip.NewWriter(c.Writer)
		defer gz.Close()

		// 包装响应writer
		c.Writer = &gzipWriter{
			ResponseWriter: c.Writer,
			Writer:         gz,
		}

		c.Next()
	})
}

// shouldCompress 判断是否应该压缩响应
func shouldCompress(req *http.Request) bool {
	// 检查Accept-Encoding头
	encoding := req.Header.Get("Accept-Encoding")
	if !strings.Contains(encoding, "gzip") {
		return false
	}

	// 检查Content-Type（只压缩文本类型）
	contentType := req.Header.Get("Content-Type")
	if contentType != "" && !isCompressibleContentType(contentType) {
		return false
	}

	return true
}

// isCompressibleContentType 判断内容类型是否可压缩
func isCompressibleContentType(contentType string) bool {
	compressibleTypes := []string{
		"application/json",
		"application/xml",
		"text/html",
		"text/plain",
		"text/css",
		"text/javascript",
		"application/javascript",
	}

	for _, t := range compressibleTypes {
		if strings.Contains(contentType, t) {
			return true
		}
	}
	return false
}

// gzipWriter 包装gin.ResponseWriter以支持gzip压缩
type gzipWriter struct {
	gin.ResponseWriter
	Writer io.Writer
}

func (g *gzipWriter) Write(data []byte) (int, error) {
	return g.Writer.Write(data)
}

func (g *gzipWriter) WriteString(s string) (int, error) {
	return g.Writer.Write([]byte(s))
}
