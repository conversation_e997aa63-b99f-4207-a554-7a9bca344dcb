package subscription

import (
	"coupon-backend/application/subscription"
	"coupon-backend/infra/ecode"
	"coupon-backend/infra/response"
	"coupon-backend/infra/utils"
	"coupon-backend/interfaces/api/dto"

	"github.com/gin-gonic/gin"
)

// SubscriptionHandler 订阅处理器
type SubscriptionHandler struct {
	subscriptionApp *subscription.SubscriptionApp
}

// NewSubscriptionHandler 创建订阅处理器
func NewSubscriptionHandler(subscriptionApp *subscription.SubscriptionApp) *SubscriptionHandler {
	return &SubscriptionHandler{
		subscriptionApp: subscriptionApp,
	}
}

// Subscribe 订阅邮箱
// @Summary 订阅邮箱
// @Description 用户订阅邮箱以接收优惠信息
// @Tags Subscription
// @Accept json
// @Produce json
// @Param request body dto.SubscribeReq true "订阅请求"
// @Success 200 {object} response.Response{data=dto.SubscribeResp}
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/subscribe [post]
func (h *SubscriptionHandler) Subscribe(c *gin.Context) {
	var req dto.SubscribeReq
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, ecode.ErrInvalidParameter.Code, err.Error())
		return
	}

	// 获取客户端信息
	req.IPAddress = utils.GetRealIP(c)
	req.UserAgent = c.GetHeader("User-Agent")

	// 如果没有指定来源，默认为website
	if req.Source == "" {
		req.Source = "website"
	}

	// 异步处理订阅，无论成功失败都立即返回成功
	go func() {
		// 使用新的context，避免请求取消影响后台处理
		ctx := c.Copy()
		_ = h.subscriptionApp.Subscribe(ctx, &req)
	}()

	// 立即返回成功响应
	response.Success(c, &dto.SubscribeResp{
		Message: "Thank you for subscribing! You'll receive amazing deals soon.",
	})
}

// Unsubscribe 取消订阅
// @Summary 取消订阅
// @Description 用户取消邮箱订阅
// @Tags Subscription
// @Accept json
// @Produce json
// @Param request body dto.UnsubscribeReq true "取消订阅请求"
// @Success 200 {object} response.Response{data=dto.UnsubscribeResp}
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/unsubscribe [post]
func (h *SubscriptionHandler) Unsubscribe(c *gin.Context) {
	var req dto.UnsubscribeReq
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, ecode.ErrInvalidParameter.Code, err.Error())
		return
	}

	if err := h.subscriptionApp.Unsubscribe(c.Request.Context(), req.Email); err != nil {
		if ecode.IsNotFound(err) {
			response.Error(c, ecode.ErrNotFound.Code, "Email not found in subscription list")
			return
		}
		response.Error(c, ecode.ErrInternalServer.Code, "Failed to unsubscribe")
		return
	}

	response.Success(c, &dto.UnsubscribeResp{
		Message: "Successfully unsubscribed from our mailing list",
	})
}

// GetSubscriptions 获取订阅列表（管理员接口）
// @Summary 获取订阅列表
// @Description 获取邮箱订阅列表，支持分页和筛选
// @Tags Subscription
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Param status query int false "订阅状态" Enums(0, 1)
// @Param email query string false "邮箱地址"
// @Param source query string false "订阅来源"
// @Success 200 {object} response.Response{data=dto.SubscriptionListResp}
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/subscriptions [get]
func (h *SubscriptionHandler) GetSubscriptions(c *gin.Context) {
	var req dto.SubscriptionListReq
	if err := c.ShouldBindQuery(&req); err != nil {
		response.Error(c, ecode.ErrInvalidParameter.Code, err.Error())
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}

	resp, err := h.subscriptionApp.ListSubscriptions(c.Request.Context(), &req)
	if err != nil {
		response.Error(c, ecode.ErrInternalServer.Code, "Failed to get subscriptions")
		return
	}

	response.Success(c, resp)
}
