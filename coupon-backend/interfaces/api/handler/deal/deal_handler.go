package deal

import (
	"coupon-backend/application/deal/appservice"
	"coupon-backend/application/deal/dto"
	"coupon-backend/infra/ecode"
	"coupon-backend/infra/response"
	"strconv"

	"github.com/gin-gonic/gin"
)

type DealHandler struct {
	dealAppService appservice.DealAppService
}

func NewDealHandler(dealAppService appservice.DealAppService) *DealHandler {
	return &DealHandler{
		dealAppService: dealAppService,
	}
}

// GetDealList 获取优惠活动列表
// @Summary 获取优惠活动列表
// @Description 获取优惠活动列表，支持分页、搜索、商家过滤等
// @Tags Deal
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Param search query string false "搜索关键词"
// @Param sort query string false "排序方式" Enums(created_at, updated_at, name, featured)
// @Param brand_id query int false "商家ID"
// @Param category_id query int false "分类ID"
// @Param category_slug query string false "分类唯一标识"
// @Param is_hot_deal query bool false "是否热门活动"
// @Param is_featured query bool false "是否推荐"
// @Param status query int false "状态"
// @Success 200 {object} response.Response{data=dto.DealListResp}
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/deals [get]
func (h *DealHandler) GetDealList(c *gin.Context) {
	var req dto.GetDealListReq
	if err := c.ShouldBindQuery(&req); err != nil {
		response.Error(c, ecode.ErrInvalidParameter.Code, err.Error())
		return
	}

	resp, appErr := h.dealAppService.GetDealList(c, &req)
	if appErr != nil {
		response.Error(c, appErr.Code, appErr.Message)
		return
	}

	response.Success(c, resp)
}

// GetDealById 根据ID获取优惠活动详情
// @Summary 根据ID获取优惠活动详情
// @Description 根据优惠活动ID获取优惠活动详情信息
// @Tags Deal
// @Accept json
// @Produce json
// @Param id path int true "优惠活动ID"
// @Success 200 {object} response.Response{data=dto.DealDetailResp}
// @Failure 400 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/deals/{id} [get]
func (h *DealHandler) GetDealById(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		response.Error(c, ecode.ErrInvalidID.Code, "invalid deal id")
		return
	}

	resp, appErr := h.dealAppService.GetDealDetailById(c, id)
	if appErr != nil {
		response.Error(c, appErr.Code, appErr.Message)
		return
	}

	response.Success(c, resp)
}

// RegisterRoutes 注册路由
func (h *DealHandler) RegisterRoutes(router *gin.RouterGroup) {
	dealGroup := router.Group("/deals")
	{
		dealGroup.GET("", h.GetDealList)
		dealGroup.GET("/:id", h.GetDealById)
	}
}
