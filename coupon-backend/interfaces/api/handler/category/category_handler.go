package category

import (
	"coupon-backend/application/category/appservice"
	"coupon-backend/application/category/dto"
	"coupon-backend/infra/ecode"
	"coupon-backend/infra/response"
	"strconv"

	"github.com/gin-gonic/gin"
)

type CategoryHandler struct {
	categoryAppService appservice.CategoryAppService
}

func NewCategoryHandler(categoryAppService appservice.CategoryAppService) *CategoryHandler {
	return &CategoryHandler{
		categoryAppService: categoryAppService,
	}
}

// GetCategoryList 获取分类列表
// @Summary 获取分类列表
// @Description 获取分类列表，支持分页和搜索
// @Tags Category
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Param search query string false "搜索关键词"
// @Param sort query string false "排序方式" Enums(created_at, updated_at, name)
// @Success 200 {object} response.Response{data=dto.CategoryListResp}
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/categories [get]
func (h *CategoryHandler) GetCategoryList(c *gin.Context) {
	var req dto.GetCategoryListReq
	if err := c.ShouldBindQuery(&req); err != nil {
		response.Error(c, ecode.ErrInvalidParameter.Code, err.Error())
		return
	}

	resp, appErr := h.categoryAppService.GetCategoryList(c, &req)
	if appErr != nil {
		response.Error(c, appErr.Code, appErr.Message)
		return
	}

	response.Success(c, resp)
}

// GetCategoryById 根据ID获取分类详情
// @Summary 根据ID获取分类详情
// @Description 根据分类ID获取分类详情信息
// @Tags Category
// @Accept json
// @Produce json
// @Param id path int true "分类ID"
// @Success 200 {object} response.Response{data=dto.CategoryDetailResp}
// @Failure 400 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/categories/{id} [get]
func (h *CategoryHandler) GetCategoryById(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		response.Error(c, ecode.ErrInvalidID.Code, "invalid category id")
		return
	}

	resp, appErr := h.categoryAppService.GetCategoryDetailById(c, id)
	if appErr != nil {
		response.Error(c, appErr.Code, appErr.Message)
		return
	}

	response.Success(c, resp)
}

// GetCategoryBySlug 根据Slug获取分类详情
// @Summary 根据Slug获取分类详情
// @Description 根据分类Slug获取分类详情信息
// @Tags Category
// @Accept json
// @Produce json
// @Param slug path string true "分类Slug"
// @Success 200 {object} response.Response{data=dto.CategoryDetailResp}
// @Failure 400 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/categories/slug/{slug} [get]
func (h *CategoryHandler) GetCategoryBySlug(c *gin.Context) {
	slug := c.Param("slug")
	if slug == "" {
		response.Error(c, ecode.ErrInvalidParameter.Code, "slug cannot be empty")
		return
	}

	resp, appErr := h.categoryAppService.GetCategoryDetailBySlug(c, slug)
	if appErr != nil {
		response.Error(c, appErr.Code, appErr.Message)
		return
	}

	response.Success(c, resp)
}

// RegisterRoutes 注册路由
func (h *CategoryHandler) RegisterRoutes(router *gin.RouterGroup) {
	categoryGroup := router.Group("/categories")
	{
		categoryGroup.GET("", h.GetCategoryList)
		categoryGroup.GET("/:id", h.GetCategoryById)
		categoryGroup.GET("/slug/:slug", h.GetCategoryBySlug)
	}
}
