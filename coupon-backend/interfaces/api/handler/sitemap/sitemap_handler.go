package sitemap

import (
	"context"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	brandAppService "coupon-backend/application/brand/appservice"
	brandDto "coupon-backend/application/brand/dto"
	categoryAppService "coupon-backend/application/category/appservice"
	categoryDto "coupon-backend/application/category/dto"
	couponAppService "coupon-backend/application/coupon/appservice"
	couponDto "coupon-backend/application/coupon/dto"
	dealAppService "coupon-backend/application/deal/appservice"
	dealDto "coupon-backend/application/deal/dto"
	"coupon-backend/config"
	"coupon-backend/infra/cache"

	"github.com/gin-gonic/gin"
)

type SitemapHandler struct {
	brandAppService    brandAppService.BrandAppService
	categoryAppService categoryAppService.CategoryAppService
	couponAppService   couponAppService.CouponAppService
	dealAppService     dealAppService.DealAppService
	cacheService       cache.CacheService
}

func NewSitemapHandler(
	brandAppService brandAppService.BrandAppService,
	categoryAppService categoryAppService.CategoryAppService,
	couponAppService couponAppService.CouponAppService,
	dealAppService dealAppService.DealAppService,
	cacheService cache.CacheService,
) *SitemapHandler {
	return &SitemapHandler{
		brandAppService:    brandAppService,
		categoryAppService: categoryAppService,
		couponAppService:   couponAppService,
		dealAppService:     dealAppService,
		cacheService:       cacheService,
	}
}

// GetIndexSitemap 获取主索引sitemap
func (h *SitemapHandler) GetIndexSitemap(c *gin.Context) {
	baseURL := getBaseURL(c)
	cacheKey := h.generateCacheKey("index", 1)

	// 检查是否强制刷新缓存
	forceRefresh := c.Query("refresh") == "1"

	var cachedContent string
	if !forceRefresh && h.cacheService.Get(context.Background(), cacheKey, &cachedContent) == nil {
		c.Header("Content-Type", "application/xml; charset=utf-8")
		c.Header("Cache-Control", "public, max-age=3600, s-maxage=3600")
		c.Header("X-Cache", "HIT")
		c.String(http.StatusOK, cachedContent)
		return
	}

	xmlContent, err := h.generateIndexSitemap(c, baseURL)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    http.StatusInternalServerError,
			"message": "failed to generate index sitemap",
		})
		return
	}

	go h.cacheService.Set(context.Background(), cacheKey, xmlContent, 12*time.Hour)

	c.Header("Content-Type", "application/xml; charset=utf-8")
	c.Header("Cache-Control", "public, max-age=3600, s-maxage=3600")
	c.Header("X-Cache", "MISS")
	c.String(http.StatusOK, xmlContent)
}

// GetBrandsSitemap 获取品牌sitemap
func (h *SitemapHandler) GetBrandsSitemap(c *gin.Context) {
	pageStr := c.Param("page")
	page, _ := strconv.Atoi(pageStr)
	if page < 1 {
		page = 1
	}

	baseURL := getBaseURL(c)
	cacheKey := h.generateCacheKey("brands", page)

	var cachedContent string
	if err := h.cacheService.Get(context.Background(), cacheKey, &cachedContent); err == nil {
		c.Header("Content-Type", "application/xml; charset=utf-8")
		c.Header("Cache-Control", "public, max-age=3600, s-maxage=3600")
		c.Header("X-Cache", "HIT")
		c.String(http.StatusOK, cachedContent)
		return
	}

	xmlContent, err := h.generateBrandsSitemap(c, baseURL, page)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    http.StatusInternalServerError,
			"message": "failed to generate brands sitemap",
		})
		return
	}

	go h.cacheService.Set(context.Background(), cacheKey, xmlContent, 6*time.Hour)

	c.Header("Content-Type", "application/xml; charset=utf-8")
	c.Header("Cache-Control", "public, max-age=3600, s-maxage=3600")
	c.Header("X-Cache", "MISS")
	c.String(http.StatusOK, xmlContent)
}

// GetCategoriesSitemap 获取分类sitemap
func (h *SitemapHandler) GetCategoriesSitemap(c *gin.Context) {
	pageStr := c.Param("page")
	page, _ := strconv.Atoi(pageStr)
	if page < 1 {
		page = 1
	}

	baseURL := getBaseURL(c)
	cacheKey := h.generateCacheKey("categories", page)

	var cachedContent string
	if err := h.cacheService.Get(context.Background(), cacheKey, &cachedContent); err == nil {
		c.Header("Content-Type", "application/xml; charset=utf-8")
		c.Header("Cache-Control", "public, max-age=3600, s-maxage=3600")
		c.Header("X-Cache", "HIT")
		c.String(http.StatusOK, cachedContent)
		return
	}

	xmlContent, err := h.generateCategoriesSitemap(c, baseURL, page)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    http.StatusInternalServerError,
			"message": "failed to generate categories sitemap",
		})
		return
	}

	go h.cacheService.Set(context.Background(), cacheKey, xmlContent, 6*time.Hour)

	c.Header("Content-Type", "application/xml; charset=utf-8")
	c.Header("Cache-Control", "public, max-age=3600, s-maxage=3600")
	c.Header("X-Cache", "MISS")
	c.String(http.StatusOK, xmlContent)
}

// GetDealsSitemap 获取优惠活动sitemap
func (h *SitemapHandler) GetDealsSitemap(c *gin.Context) {
	pageStr := c.Param("page")
	page, _ := strconv.Atoi(pageStr)
	if page < 1 {
		page = 1
	}

	baseURL := getBaseURL(c)
	cacheKey := h.generateCacheKey("deals", page)

	var cachedContent string
	if err := h.cacheService.Get(context.Background(), cacheKey, &cachedContent); err == nil {
		c.Header("Content-Type", "application/xml; charset=utf-8")
		c.Header("Cache-Control", "public, max-age=3600, s-maxage=3600")
		c.Header("X-Cache", "HIT")
		c.String(http.StatusOK, cachedContent)
		return
	}

	xmlContent, err := h.generateDealsSitemap(c, baseURL, page)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    http.StatusInternalServerError,
			"message": "failed to generate deals sitemap",
		})
		return
	}

	go h.cacheService.Set(context.Background(), cacheKey, xmlContent, 6*time.Hour)

	c.Header("Content-Type", "application/xml; charset=utf-8")
	c.Header("Cache-Control", "public, max-age=3600, s-maxage=3600")
	c.Header("X-Cache", "MISS")
	c.String(http.StatusOK, xmlContent)
}

// GetCouponsSitemap 获取优惠券sitemap
func (h *SitemapHandler) GetCouponsSitemap(c *gin.Context) {
	pageStr := c.Param("page")
	page, _ := strconv.Atoi(pageStr)
	if page < 1 {
		page = 1
	}

	baseURL := getBaseURL(c)
	cacheKey := h.generateCacheKey("coupons", page)

	var cachedContent string
	if err := h.cacheService.Get(context.Background(), cacheKey, &cachedContent); err == nil {
		c.Header("Content-Type", "application/xml; charset=utf-8")
		c.Header("Cache-Control", "public, max-age=3600, s-maxage=3600")
		c.Header("X-Cache", "HIT")
		c.String(http.StatusOK, cachedContent)
		return
	}

	xmlContent, err := h.generateCouponsSitemap(c, baseURL, page)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    http.StatusInternalServerError,
			"message": "failed to generate coupons sitemap",
		})
		return
	}

	go h.cacheService.Set(context.Background(), cacheKey, xmlContent, 6*time.Hour)

	c.Header("Content-Type", "application/xml; charset=utf-8")
	c.Header("Cache-Control", "public, max-age=3600, s-maxage=3600")
	c.Header("X-Cache", "MISS")
	c.String(http.StatusOK, xmlContent)
}

// GetStaticSitemap 获取静态页面sitemap
func (h *SitemapHandler) GetStaticSitemap(c *gin.Context) {
	baseURL := getBaseURL(c)
	cacheKey := h.generateCacheKey("static", 1)

	var cachedContent string
	if err := h.cacheService.Get(context.Background(), cacheKey, &cachedContent); err == nil {
		c.Header("Content-Type", "application/xml; charset=utf-8")
		c.Header("Cache-Control", "public, max-age=3600, s-maxage=3600")
		c.Header("X-Cache", "HIT")
		c.String(http.StatusOK, cachedContent)
		return
	}

	xmlContent, err := h.generateStaticSitemap(c, baseURL)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    http.StatusInternalServerError,
			"message": "failed to generate static sitemap",
		})
		return
	}

	go h.cacheService.Set(context.Background(), cacheKey, xmlContent, 24*time.Hour)

	c.Header("Content-Type", "application/xml; charset=utf-8")
	c.Header("Cache-Control", "public, max-age=3600, s-maxage=3600")
	c.Header("X-Cache", "MISS")
	c.String(http.StatusOK, xmlContent)
}

// RegisterRoutes 注册路由
func (h *SitemapHandler) RegisterRoutes(router *gin.RouterGroup) {
	router.GET("/sitemap.xml", h.GetIndexSitemap)
	router.GET("/sitemap-static.xml", h.GetStaticSitemap)
	router.GET("/sitemap-brands-:page.xml", h.GetBrandsSitemap)
	router.GET("/sitemap-categories-:page.xml", h.GetCategoriesSitemap)
	router.GET("/sitemap-deals-:page.xml", h.GetDealsSitemap)
	router.GET("/sitemap-coupons-:page.xml", h.GetCouponsSitemap)
}

// getBaseURL 获取基础URL
func getBaseURL(c *gin.Context) string {
	// 直接使用配置文件中的website.url数据
	return getFrontendBaseURL()
}

// getFrontendBaseURL 获取前端基础URL（用于sitemap索引）
func getFrontendBaseURL() string {
	// 从配置文件获取网站URL
	if config.GlobalConfig != nil && config.GlobalConfig.Website.URL != "" {
		return config.GlobalConfig.Website.URL
	}

	return "http://localhost:3001"
}

// generateCacheKey 生成缓存键
func (h *SitemapHandler) generateCacheKey(sitemapType string, page int) string {
	return fmt.Sprintf("sitemap:%s:page:%d", sitemapType, page)
}

// generateIndexSitemap 生成主索引sitemap
func (h *SitemapHandler) generateIndexSitemap(c *gin.Context, baseURL string) (string, error) {
	// 对于主索引sitemap，使用前端URL而不是后端URL
	frontendURL := getFrontendBaseURL()
	const maxURLsPerSitemap = 50000

	// 获取各类型数据的总数
	brandCount, err := h.brandAppService.GetBrandCount(c)
	if err != nil {
		brandCount = 0
	}

	categoryCount, err := h.categoryAppService.GetCategoryCount(c)
	if err != nil {
		categoryCount = 0
	}

	dealCount, err := h.dealAppService.GetDealCount(c)
	if err != nil {
		dealCount = 0
	}

	couponCount, err := h.couponAppService.GetCouponCount(c)
	if err != nil {
		couponCount = 0
	}

	var sitemap strings.Builder
	sitemap.WriteString(`<?xml version="1.0" encoding="UTF-8"?>`)
	sitemap.WriteString("\n")
	sitemap.WriteString(`<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">`)
	sitemap.WriteString("\n")

	// 静态页面sitemap
	sitemap.WriteString(fmt.Sprintf(`  <sitemap>
    <loc>%s/sitemap-static.xml</loc>
    <lastmod>%s</lastmod>
  </sitemap>`, frontendURL, time.Now().Format("2006-01-02")))
	sitemap.WriteString("\n")

	// 品牌sitemap
	brandPages := int((brandCount + maxURLsPerSitemap - 1) / maxURLsPerSitemap)
	if brandPages == 0 {
		brandPages = 1 // 至少生成一个页面
	}
	for i := 1; i <= brandPages; i++ {
		sitemap.WriteString(fmt.Sprintf(`  <sitemap>
    <loc>%s/sitemap-brands-%d.xml</loc>
    <lastmod>%s</lastmod>
  </sitemap>`, frontendURL, i, time.Now().Format("2006-01-02")))
		sitemap.WriteString("\n")
	}

	// 分类sitemap
	categoryPages := int((categoryCount + maxURLsPerSitemap - 1) / maxURLsPerSitemap)
	if categoryPages == 0 {
		categoryPages = 1 // 至少生成一个页面
	}
	for i := 1; i <= categoryPages; i++ {
		sitemap.WriteString(fmt.Sprintf(`  <sitemap>
    <loc>%s/sitemap-categories-%d.xml</loc>
    <lastmod>%s</lastmod>
  </sitemap>`, frontendURL, i, time.Now().Format("2006-01-02")))
		sitemap.WriteString("\n")
	}

	// 优惠活动sitemap
	dealPages := int((dealCount + maxURLsPerSitemap - 1) / maxURLsPerSitemap)
	if dealPages == 0 {
		dealPages = 1 // 至少生成一个页面
	}
	for i := 1; i <= dealPages; i++ {
		sitemap.WriteString(fmt.Sprintf(`  <sitemap>
    <loc>%s/sitemap-deals-%d.xml</loc>
    <lastmod>%s</lastmod>
  </sitemap>`, frontendURL, i, time.Now().Format("2006-01-02")))
		sitemap.WriteString("\n")
	}

	// 优惠券sitemap
	couponPages := int((couponCount + maxURLsPerSitemap - 1) / maxURLsPerSitemap)
	if couponPages == 0 {
		couponPages = 1 // 至少生成一个页面
	}
	for i := 1; i <= couponPages; i++ {
		sitemap.WriteString(fmt.Sprintf(`  <sitemap>
    <loc>%s/sitemap-coupons-%d.xml</loc>
    <lastmod>%s</lastmod>
  </sitemap>`, frontendURL, i, time.Now().Format("2006-01-02")))
		sitemap.WriteString("\n")
	}

	sitemap.WriteString("</sitemapindex>")
	return sitemap.String(), nil
}

// generateBrandsSitemap 生成品牌sitemap
func (h *SitemapHandler) generateBrandsSitemap(c *gin.Context, baseURL string, page int) (string, error) {
	// 使用前端URL而不是后端URL
	frontendURL := getFrontendBaseURL()
	const maxURLsPerSitemap = 50000
	const apiPageSize = 100 // 后端API的最大分页限制

	var allBrands []*brandDto.BrandDetailResp

	// 计算需要获取的数据范围
	startIndex := (page - 1) * maxURLsPerSitemap
	endIndex := startIndex + maxURLsPerSitemap

	// 计算需要请求的API页面范围
	startAPIPage := (startIndex / apiPageSize) + 1
	endAPIPage := ((endIndex - 1) / apiPageSize) + 1

	// 分批获取数据
	for apiPage := startAPIPage; apiPage <= endAPIPage; apiPage++ {
		req := &brandDto.GetBrandListReq{
			Page:     apiPage,
			PageSize: apiPageSize,
		}

		resp, err := h.brandAppService.GetBrandList(c, req)
		if err != nil {
			return "", fmt.Errorf("failed to get brands page %d: %v", apiPage, err)
		}

		allBrands = append(allBrands, resp.BrandList...)

		// 如果返回的数据少于请求的数量，说明已经到最后一页
		if len(resp.BrandList) < apiPageSize {
			break
		}
	}

	// 计算当前sitemap页面应该包含的数据范围
	sitemapStartIndex := startIndex - (startAPIPage-1)*apiPageSize
	sitemapEndIndex := sitemapStartIndex + maxURLsPerSitemap

	// 确保不超出实际数据范围
	if sitemapStartIndex < 0 {
		sitemapStartIndex = 0
	}
	if sitemapEndIndex > len(allBrands) {
		sitemapEndIndex = len(allBrands)
	}

	// 获取当前页面的数据
	var pageData []*brandDto.BrandDetailResp
	if sitemapStartIndex < len(allBrands) {
		pageData = allBrands[sitemapStartIndex:sitemapEndIndex]
	}

	var sitemap strings.Builder
	sitemap.WriteString(`<?xml version="1.0" encoding="UTF-8"?>`)
	sitemap.WriteString("\n")
	sitemap.WriteString(`<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">`)
	sitemap.WriteString("\n")

	for _, brand := range pageData {
		sitemap.WriteString(fmt.Sprintf(`  <url>\n    <loc>%s/brands/%s</loc>\n    <changefreq>daily</changefreq>\n    <priority>1.0</priority>\n    <lastmod>%s</lastmod>\n  </url>`, frontendURL, brand.UniqueName, time.Now().Format("2006-01-02")))
		sitemap.WriteString("\n")
	}

	sitemap.WriteString("</urlset>")
	return sitemap.String(), nil
}

// generateCategoriesSitemap 生成分类sitemap
func (h *SitemapHandler) generateCategoriesSitemap(c *gin.Context, baseURL string, page int) (string, error) {
	// 使用前端URL而不是后端URL
	frontendURL := getFrontendBaseURL()
	const maxURLsPerSitemap = 50000
	const apiPageSize = 100 // 后端API的最大分页限制

	var allCategories []*categoryDto.CategoryDetailResp

	// 计算需要获取的数据范围
	startIndex := (page - 1) * maxURLsPerSitemap
	endIndex := startIndex + maxURLsPerSitemap

	// 计算需要请求的API页面范围
	startAPIPage := (startIndex / apiPageSize) + 1
	endAPIPage := ((endIndex - 1) / apiPageSize) + 1

	// 分批获取数据
	for apiPage := startAPIPage; apiPage <= endAPIPage; apiPage++ {
		req := &categoryDto.GetCategoryListReq{
			Page:     apiPage,
			PageSize: apiPageSize,
		}

		resp, err := h.categoryAppService.GetCategoryList(c, req)
		if err != nil {
			return "", fmt.Errorf("failed to get categories page %d: %v", apiPage, err)
		}

		allCategories = append(allCategories, resp.CategoryList...)

		// 如果返回的数据少于请求的数量，说明已经到最后一页
		if len(resp.CategoryList) < apiPageSize {
			break
		}
	}

	// 计算当前sitemap页面应该包含的数据范围
	sitemapStartIndex := startIndex - (startAPIPage-1)*apiPageSize
	sitemapEndIndex := sitemapStartIndex + maxURLsPerSitemap

	// 确保不超出实际数据范围
	if sitemapStartIndex < 0 {
		sitemapStartIndex = 0
	}
	if sitemapEndIndex > len(allCategories) {
		sitemapEndIndex = len(allCategories)
	}

	// 获取当前页面的数据
	var pageData []*categoryDto.CategoryDetailResp
	if sitemapStartIndex < len(allCategories) {
		pageData = allCategories[sitemapStartIndex:sitemapEndIndex]
	}

	var sitemap strings.Builder
	sitemap.WriteString(`<?xml version="1.0" encoding="UTF-8"?>`)
	sitemap.WriteString("\n")
	sitemap.WriteString(`<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">`)
	sitemap.WriteString("\n")

	for _, category := range pageData {
		sitemap.WriteString(fmt.Sprintf(`  <url>\n    <loc>%s/categories/%s</loc>\n    <changefreq>daily</changefreq>\n    <priority>1.0</priority>\n    <lastmod>%s</lastmod>\n  </url>`, frontendURL, category.Slug, time.Now().Format("2006-01-02")))
		sitemap.WriteString("\n")
	}

	sitemap.WriteString("</urlset>")
	return sitemap.String(), nil
}

// generateDealsSitemap 生成优惠活动sitemap
func (h *SitemapHandler) generateDealsSitemap(c *gin.Context, baseURL string, page int) (string, error) {
	// 使用前端URL而不是后端URL
	frontendURL := getFrontendBaseURL()
	const maxURLsPerSitemap = 50000
	const apiPageSize = 100 // 后端API的最大分页限制

	var allDeals []*dealDto.DealDetailResp

	// 计算需要获取的数据范围
	startIndex := (page - 1) * maxURLsPerSitemap
	endIndex := startIndex + maxURLsPerSitemap

	// 计算需要请求的API页面范围
	startAPIPage := (startIndex / apiPageSize) + 1
	endAPIPage := ((endIndex - 1) / apiPageSize) + 1

	// 分批获取数据
	for apiPage := startAPIPage; apiPage <= endAPIPage; apiPage++ {
		req := &dealDto.GetDealListReq{
			Page:     apiPage,
			PageSize: apiPageSize,
		}

		resp, err := h.dealAppService.GetDealList(c, req)
		if err != nil {
			return "", fmt.Errorf("failed to get deals page %d: %v", apiPage, err)
		}

		allDeals = append(allDeals, resp.DealList...)

		// 如果返回的数据少于请求的数量，说明已经到最后一页
		if len(resp.DealList) < apiPageSize {
			break
		}
	}

	// 计算当前sitemap页面应该包含的数据范围
	sitemapStartIndex := startIndex - (startAPIPage-1)*apiPageSize
	sitemapEndIndex := sitemapStartIndex + maxURLsPerSitemap

	// 确保不超出实际数据范围
	if sitemapStartIndex < 0 {
		sitemapStartIndex = 0
	}
	if sitemapEndIndex > len(allDeals) {
		sitemapEndIndex = len(allDeals)
	}

	// 获取当前页面的数据
	var pageData []*dealDto.DealDetailResp
	if sitemapStartIndex < len(allDeals) {
		pageData = allDeals[sitemapStartIndex:sitemapEndIndex]
	}

	var sitemap strings.Builder
	sitemap.WriteString(`<?xml version="1.0" encoding="UTF-8"?>`)
	sitemap.WriteString("\n")
	sitemap.WriteString(`<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">`)
	sitemap.WriteString("\n")

	for _, deal := range pageData {
		sitemap.WriteString(fmt.Sprintf(`  <url>\n    <loc>%s/deals/%d</loc>\n    <changefreq>daily</changefreq>\n    <priority>1.0</priority>\n    <lastmod>%s</lastmod>\n  </url>`, frontendURL, deal.ID, time.Now().Format("2006-01-02")))
		sitemap.WriteString("\n")
	}

	sitemap.WriteString("</urlset>")
	return sitemap.String(), nil
}

// generateCouponsSitemap 生成优惠券sitemap
func (h *SitemapHandler) generateCouponsSitemap(c *gin.Context, baseURL string, page int) (string, error) {
	// 使用前端URL而不是后端URL
	frontendURL := getFrontendBaseURL()
	const maxURLsPerSitemap = 50000
	const apiPageSize = 100 // 后端API的最大分页限制

	var allCoupons []*couponDto.CouponDetailResp

	// 计算需要获取的数据范围
	startIndex := (page - 1) * maxURLsPerSitemap
	endIndex := startIndex + maxURLsPerSitemap

	// 计算需要请求的API页面范围
	startAPIPage := (startIndex / apiPageSize) + 1
	endAPIPage := ((endIndex - 1) / apiPageSize) + 1

	// 分批获取数据
	for apiPage := startAPIPage; apiPage <= endAPIPage; apiPage++ {
		req := &couponDto.GetCouponListReq{
			Page:     apiPage,
			PageSize: apiPageSize,
		}

		resp, err := h.couponAppService.GetCouponList(c, req)
		if err != nil {
			return "", fmt.Errorf("failed to get coupons page %d: %v", apiPage, err)
		}

		allCoupons = append(allCoupons, resp.CouponList...)

		// 如果返回的数据少于请求的数量，说明已经到最后一页
		if len(resp.CouponList) < apiPageSize {
			break
		}
	}

	// 计算当前sitemap页面应该包含的数据范围
	sitemapStartIndex := startIndex - (startAPIPage-1)*apiPageSize
	sitemapEndIndex := sitemapStartIndex + maxURLsPerSitemap

	// 确保不超出实际数据范围
	if sitemapStartIndex < 0 {
		sitemapStartIndex = 0
	}
	if sitemapEndIndex > len(allCoupons) {
		sitemapEndIndex = len(allCoupons)
	}

	// 获取当前页面的数据
	var pageData []*couponDto.CouponDetailResp
	if sitemapStartIndex < len(allCoupons) {
		pageData = allCoupons[sitemapStartIndex:sitemapEndIndex]
	}

	var sitemap strings.Builder
	sitemap.WriteString(`<?xml version="1.0" encoding="UTF-8"?>`)
	sitemap.WriteString("\n")
	sitemap.WriteString(`<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">`)
	sitemap.WriteString("\n")

	for _, coupon := range pageData {
		sitemap.WriteString(fmt.Sprintf(`  <url>\n    <loc>%s/coupons/%d</loc>\n    <changefreq>daily</changefreq>\n    <priority>1.0</priority>\n    <lastmod>%s</lastmod>\n  </url>`, frontendURL, coupon.ID, time.Now().Format("2006-01-02")))
		sitemap.WriteString("\n")
	}

	sitemap.WriteString("</urlset>")
	return sitemap.String(), nil
}

// generateStaticSitemap 生成静态页面sitemap
func (h *SitemapHandler) generateStaticSitemap(c *gin.Context, baseURL string) (string, error) {
	frontendURL := getFrontendBaseURL()
	var sitemap strings.Builder
	sitemap.WriteString(`<?xml version="1.0" encoding="UTF-8"?>`)
	sitemap.WriteString("\n")
	sitemap.WriteString(`<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">`)
	sitemap.WriteString("\n")

	staticPages := []string{
		"/",
		"/about-us",
		"/contact-us",
		"/privacy-policy",
		"/terms-of-service",
	}

	for _, page := range staticPages {
		sitemap.WriteString(fmt.Sprintf(`  <url>\n    <loc>%s%s</loc>\n    <changefreq>weekly</changefreq>\n    <priority>0.8</priority>\n    <lastmod>%s</lastmod>\n  </url>`, frontendURL, page, time.Now().Format("2006-01-02")))
		sitemap.WriteString("\n")
	}

	sitemap.WriteString("</urlset>")
	return sitemap.String(), nil
}
