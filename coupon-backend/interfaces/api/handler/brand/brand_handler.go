package brand

import (
	"coupon-backend/application/brand/appservice"
	"coupon-backend/application/brand/dto"
	"coupon-backend/infra/ecode"
	"coupon-backend/infra/response"
	"strconv"

	"github.com/gin-gonic/gin"
)

type BrandHandler struct {
	brandAppService appservice.BrandAppService
}

func NewBrandHandler(brandAppService appservice.BrandAppService) *BrandHandler {
	return &BrandHandler{
		brandAppService: brandAppService,
	}
}

// GetBrandList 获取商家列表
// @Summary 获取商家列表
// @Description 获取商家列表，支持分页、搜索、分类过滤等
// @Tags Brand
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Param search query string false "搜索关键词"
// @Param sort query string false "排序方式" Enums(created_at, updated_at, name, featured)
// @Param featured query bool false "是否推荐"
// @Param category_id query int false "分类ID"
// @Param starts_with query string false "名称首字母"
// @Success 200 {object} response.Response{data=dto.BrandListResp}
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/brands [get]
func (h *BrandHandler) GetBrandList(c *gin.Context) {
	var req dto.GetBrandListReq
	if err := c.ShouldBindQuery(&req); err != nil {
		response.Error(c, ecode.ErrInvalidParameter.Code, err.Error())
		return
	}

	resp, appErr := h.brandAppService.GetBrandList(c, &req)
	if appErr != nil {
		response.Error(c, appErr.Code, appErr.Message)
		return
	}

	response.Success(c, resp)
}

// GetBrandById 根据ID获取商家详情
// @Summary 根据ID获取商家详情
// @Description 根据商家ID获取商家详情信息
// @Tags Brand
// @Accept json
// @Produce json
// @Param id path int true "商家ID"
// @Success 200 {object} response.Response{data=dto.BrandDetailResp}
// @Failure 400 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/brands/{id} [get]
func (h *BrandHandler) GetBrandById(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		response.Error(c, ecode.ErrInvalidID.Code, "invalid brand id")
		return
	}

	resp, appErr := h.brandAppService.GetBrandDetailById(c, id)
	if appErr != nil {
		response.Error(c, appErr.Code, appErr.Message)
		return
	}

	response.Success(c, resp)
}

// GetBrandByUniqueName 根据UniqueName获取商家详情
// @Summary 根据UniqueName获取商家详情
// @Description 根据商家UniqueName获取商家详情信息
// @Tags Brand
// @Accept json
// @Produce json
// @Param unique_name path string true "商家唯一名称"
// @Success 200 {object} response.Response{data=dto.BrandDetailResp}
// @Failure 400 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/brands/name/{unique_name} [get]
func (h *BrandHandler) GetBrandByUniqueName(c *gin.Context) {
	uniqueName := c.Param("unique_name")
	if uniqueName == "" {
		response.Error(c, ecode.ErrInvalidParameter.Code, "unique_name cannot be empty")
		return
	}

	resp, appErr := h.brandAppService.GetBrandDetailByUniqueName(c, uniqueName)
	if appErr != nil {
		response.Error(c, appErr.Code, appErr.Message)
		return
	}

	response.Success(c, resp)
}

// RegisterRoutes 注册路由
func (h *BrandHandler) RegisterRoutes(router *gin.RouterGroup) {
	brandGroup := router.Group("/brands")
	{
		brandGroup.GET("", h.GetBrandList)
		brandGroup.GET("/:id", h.GetBrandById)
		brandGroup.GET("/name/:unique_name", h.GetBrandByUniqueName)
	}
}
