package search

import (
	"coupon-backend/application/search/appservice"
	"coupon-backend/application/search/dto"
	"coupon-backend/infra/ecode"
	"coupon-backend/infra/response"

	"github.com/gin-gonic/gin"
)

type SearchHandler struct {
	searchAppService appservice.SearchAppService
}

func NewSearchHandler(searchAppService appservice.SearchAppService) *SearchHandler {
	return &SearchHandler{
		searchAppService: searchAppService,
	}
}

// Search 统一搜索接口
// @Summary 统一搜索
// @Description 可以同时搜索商家、优惠券、优惠活动，响应中包含搜索结果和统计信息
// @Tags Search
// @Accept json
// @Produce json
// @Param query query string true "搜索关键词"
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Param type query string false "搜索类型" Enums(all, brand, coupon, deal) default(all)
// @Param sort query string false "排序方式" Enums(created_at, updated_at, name, featured)
// @Success 200 {object} response.Response{data=dto.SearchResponse}
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/search [get]
func (h *SearchHandler) Search(c *gin.Context) {
	var req dto.SearchRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		response.Error(c, ecode.ErrInvalidParameter.Code, err.Error())
		return
	}

	// 验证查询参数
	if req.Query == "" {
		response.Error(c, ecode.ErrInvalidParameter.Code, "query parameter is required")
		return
	}

	resp, appErr := h.searchAppService.Search(c, &req)
	if appErr != nil {
		response.Error(c, appErr.Code, appErr.Message)
		return
	}

	response.Success(c, resp)
}

// RegisterRoutes 注册路由
func (h *SearchHandler) RegisterRoutes(router *gin.RouterGroup) {
	searchGroup := router.Group("/search")
	{
		searchGroup.GET("", h.Search)
	}
}
