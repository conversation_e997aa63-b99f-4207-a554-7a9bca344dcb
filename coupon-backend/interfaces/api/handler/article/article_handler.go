package article

import (
	"coupon-backend/application/article/appservice"
	"coupon-backend/application/article/dto"
	"coupon-backend/infra/ecode"
	"coupon-backend/infra/response"
	"strconv"

	"github.com/gin-gonic/gin"
)

type ArticleHandler struct {
	articleAppService appservice.ArticleAppService
}

func NewArticleHandler(articleAppService appservice.ArticleAppService) *ArticleHandler {
	return &ArticleHandler{
		articleAppService: articleAppService,
	}
}

// GetArticleList 获取文章列表
// @Summary 获取文章列表
// @Description 获取文章列表，支持分页、搜索、筛选等
// @Tags Article
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Param search query string false "搜索关键词"
// @Param sort query string false "排序方式" Enums(created_at, updated_at, published_at, title, featured)
// @Param brand_id query int false "商家ID"
// @Param category_id query int false "分类ID"
// @Param featured query bool false "是否推荐"
// @Param published query bool false "是否发布"
// @Success 200 {object} response.Response{data=dto.ArticleListResp}
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/articles [get]
func (h *ArticleHandler) GetArticleList(c *gin.Context) {
	var req dto.GetArticleListReq
	if err := c.ShouldBindQuery(&req); err != nil {
		response.Error(c, ecode.ErrInvalidParameter.Code, err.Error())
		return
	}

	resp, appErr := h.articleAppService.GetArticleList(c, &req)
	if appErr != nil {
		response.Error(c, appErr.Code, appErr.Message)
		return
	}

	response.Success(c, resp)
}

// GetArticleById 根据ID获取文章详情
// @Summary 根据ID获取文章详情
// @Description 根据文章ID获取文章详情信息，包含关联的商家、分类、优惠券、优惠活动信息
// @Tags Article
// @Accept json
// @Produce json
// @Param id path int true "文章ID"
// @Success 200 {object} response.Response{data=dto.ArticleDetailResp}
// @Failure 400 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/articles/{id} [get]
func (h *ArticleHandler) GetArticleById(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		response.Error(c, ecode.ErrInvalidID.Code, "invalid article id")
		return
	}

	resp, appErr := h.articleAppService.GetArticleDetailById(c, id)
	if appErr != nil {
		response.Error(c, appErr.Code, appErr.Message)
		return
	}

	response.Success(c, resp)
}

// GetArticleBySlug 根据Slug获取文章详情
// @Summary 根据Slug获取文章详情
// @Description 根据文章Slug获取文章详情信息，包含关联的商家、分类、优惠券、优惠活动信息
// @Tags Article
// @Accept json
// @Produce json
// @Param slug path string true "文章Slug"
// @Success 200 {object} response.Response{data=dto.ArticleDetailResp}
// @Failure 400 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/articles/slug/{slug} [get]
func (h *ArticleHandler) GetArticleBySlug(c *gin.Context) {
	slug := c.Param("slug")
	if slug == "" {
		response.Error(c, ecode.ErrInvalidParameter.Code, "slug parameter is required")
		return
	}

	resp, appErr := h.articleAppService.GetArticleDetailBySlug(c, slug)
	if appErr != nil {
		response.Error(c, appErr.Code, appErr.Message)
		return
	}

	response.Success(c, resp)
}

// RegisterRoutes 注册路由
func (h *ArticleHandler) RegisterRoutes(router *gin.RouterGroup) {
	articleGroup := router.Group("/articles")
	{
		articleGroup.GET("", h.GetArticleList)
		articleGroup.GET("/:id", h.GetArticleById)
		articleGroup.GET("/slug/:slug", h.GetArticleBySlug)
	}
}
