package coupon

import (
	"coupon-backend/application/coupon/appservice"
	"coupon-backend/application/coupon/dto"
	"coupon-backend/infra/ecode"
	"coupon-backend/infra/response"
	"strconv"

	"github.com/gin-gonic/gin"
)

type CouponHandler struct {
	couponAppService appservice.CouponAppService
}

func NewCouponHandler(couponAppService appservice.CouponAppService) *CouponHandler {
	return &CouponHandler{
		couponAppService: couponAppService,
	}
}

// GetCouponList 获取优惠券列表
// @Summary 获取优惠券列表
// @Description 获取优惠券列表，支持分页、搜索、商家过滤等
// @Tags Coupon
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Param search query string false "搜索关键词"
// @Param sort query string false "排序方式" Enums(created_at, updated_at, name, featured)
// @Param brand_id query int false "商家ID"
// @Param category_id query int false "分类ID"
// @Param category_slug query string false "分类唯一标识"
// @Param is_featured query bool false "是否推荐"
// @Param is_exclusive query bool false "是否独家"
// @Param status query int false "状态"
// @Success 200 {object} response.Response{data=dto.CouponListResp}
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/coupons [get]
func (h *CouponHandler) GetCouponList(c *gin.Context) {
	var req dto.GetCouponListReq
	if err := c.ShouldBindQuery(&req); err != nil {
		response.Error(c, ecode.ErrInvalidParameter.Code, err.Error())
		return
	}

	resp, appErr := h.couponAppService.GetCouponList(c, &req)
	if appErr != nil {
		response.Error(c, appErr.Code, appErr.Message)
		return
	}

	response.Success(c, resp)
}

// GetCouponById 根据ID获取优惠券详情
// @Summary 根据ID获取优惠券详情
// @Description 根据优惠券ID获取优惠券详情信息
// @Tags Coupon
// @Accept json
// @Produce json
// @Param id path int true "优惠券ID"
// @Success 200 {object} response.Response{data=dto.CouponDetailResp}
// @Failure 400 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/coupons/{id} [get]
func (h *CouponHandler) GetCouponById(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		response.Error(c, ecode.ErrInvalidID.Code, "invalid coupon id")
		return
	}

	resp, appErr := h.couponAppService.GetCouponDetailById(c, id)
	if appErr != nil {
		response.Error(c, appErr.Code, appErr.Message)
		return
	}

	response.Success(c, resp)
}

// RegisterRoutes 注册路由
func (h *CouponHandler) RegisterRoutes(router *gin.RouterGroup) {
	couponGroup := router.Group("/coupons")
	{
		couponGroup.GET("", h.GetCouponList)
		couponGroup.GET("/:id", h.GetCouponById)
	}
}
