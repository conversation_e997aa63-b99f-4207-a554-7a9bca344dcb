package main

import (
	"fmt"
	"log"

	"coupon-backend/infra/constant"
	"coupon-backend/infra/external_gateway/linkbuxlib"
)

func main() {
	fmt.Println("Debug Linkbux API")
	fmt.Println("=================")

	// Get linkbux config
	var linkbuxConfig map[string]interface{}
	for _, accountConfig := range constant.MerchantsAccountList {
		if accountConfig["type"].(string) == constant.AccountTypeLinkbux {
			linkbuxConfig = accountConfig
			break
		}
	}

	if linkbuxConfig == nil {
		log.Fatal("Linkbux config not found")
	}

	// Test with very small limit
	testConfig := make(map[string]interface{})
	for k, v := range linkbuxConfig {
		testConfig[k] = v
	}
	testConfig["limit"] = 2 // Very small for testing

	fmt.Printf("Testing with limit: %d\n", testConfig["limit"])
	fmt.Printf("Token: %s...\n", testConfig["token"].(string)[:10])

	merchants, err := linkbuxlib.BatchGetMerchants("linkbux01", testConfig)
	if err != nil {
		fmt.Printf("❌ Error: %v\n", err)
		return
	}

	fmt.Printf("✅ Success! Got %d merchants\n", len(merchants))
	for i, merchant := range merchants {
		fmt.Printf("Merchant %d: %s (%s)\n", i+1, merchant["name"], merchant["site_url"])
	}
}
