package config

import (
	"fmt"
	"log"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/joho/godotenv"
	"gopkg.in/yaml.v3"
)

// Config 应用配置
type Config struct {
	Server   ServerConfig   `yaml:"server"`
	Database DatabaseConfig `yaml:"database"`
	Redis    RedisConfig    `yaml:"redis"`
	Log      LogConfig      `yaml:"log"`
	Task     TaskConfig     `yaml:"task"`
	Website  WebsiteConfig  `yaml:"website"`
}

// ServerConfig 服务器配置
type ServerConfig struct {
	Port         int           `yaml:"port"`
	Mode         string        `yaml:"mode"` // debug, release, test
	ReadTimeout  time.Duration `yaml:"read_timeout"`
	WriteTimeout time.Duration `yaml:"write_timeout"`
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Host         string        `yaml:"host"`
	Port         int           `yaml:"port"`
	User         string        `yaml:"user"`
	Password     string        `yaml:"password"`
	DBName       string        `yaml:"db_name"`
	SSLMode      string        `yaml:"ssl_mode"`
	MaxIdleConns int           `yaml:"max_idle_conns"`
	MaxOpenConns int           `yaml:"max_open_conns"`
	MaxLifetime  time.Duration `yaml:"max_lifetime"`
}

// RedisConfig Redis配置
type RedisConfig struct {
	Host         string        `yaml:"host"`
	Port         int           `yaml:"port"`
	Password     string        `yaml:"password"`
	DB           int           `yaml:"db"`
	PoolSize     int           `yaml:"pool_size"`
	MinIdleConns int           `yaml:"min_idle_conns"`
	DialTimeout  time.Duration `yaml:"dial_timeout"`
	ReadTimeout  time.Duration `yaml:"read_timeout"`
	WriteTimeout time.Duration `yaml:"write_timeout"`
}

// LogConfig 日志配置
type LogConfig struct {
	Level    string `yaml:"level"`
	Filename string `yaml:"filename"`
	MaxSize  int    `yaml:"max_size"`
	MaxAge   int    `yaml:"max_age"`
	Compress bool   `yaml:"compress"`
}

// TaskConfig 任务配置
type TaskConfig struct {
	BrandSyncCron  string        `yaml:"brand_sync_cron"`
	CouponSyncCron string        `yaml:"coupon_sync_cron"`
	DealSyncCron   string        `yaml:"deal_sync_cron"`
	TaskTimeout    time.Duration `yaml:"task_timeout"`
}

// WebsiteConfig 网站配置
type WebsiteConfig struct {
	Domain string `yaml:"domain"`
	URL    string `yaml:"url"`
}

var GlobalConfig *Config

// LoadConfig 加载配置
func LoadConfig() *Config {
	return LoadConfigForService("api")
}

// LoadConfigForService 为指定服务加载配置
func LoadConfigForService(serviceType string) *Config {
	// 1. 加载 .env 文件
	env := loadEnvFile()

	// 2. 根据环境和服务类型加载对应的配置文件
	config := loadConfigFile(env, serviceType)

	GlobalConfig = config
	log.Printf("Loaded config for service: %s, environment: %s", serviceType, env)
	return config
}

// loadEnvFile 加载 .env 文件并获取环境变量
func loadEnvFile() string {
	// 加载 .env 文件
	if err := godotenv.Load(); err != nil {
		log.Printf("Warning: .env file not found, using default environment: %v", err)
	}

	// 获取环境变量，默认为 live
	env := strings.ToLower(strings.TrimSpace(os.Getenv("ENV")))
	if env == "" {
		env = "live"
	}

	// 验证环境值
	if env != "local" && env != "live" {
		log.Printf("Warning: invalid ENV value '%s', using 'live'", env)
		env = "live"
	}

	return env
}

// loadConfigFile 根据环境和服务类型加载配置文件
func loadConfigFile(env, serviceType string) *Config {
	// 构建配置文件路径
	configPath := filepath.Join("configs", env, serviceType+".yaml")

	// 检查文件是否存在
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		log.Fatalf("Config file not found: %s", configPath)
	}

	// 读取配置文件
	data, err := os.ReadFile(configPath)
	if err != nil {
		log.Fatalf("Failed to read config file %s: %v", configPath, err)
	}

	// 解析YAML配置
	var config Config
	if err := yaml.Unmarshal(data, &config); err != nil {
		log.Fatalf("Failed to parse config file %s: %v", configPath, err)
	}

	log.Printf("Successfully loaded config from: %s", configPath)
	return &config
}

// GetDSN 获取数据库连接字符串
func (c *DatabaseConfig) GetDSN() string {
	return fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=%s",
		c.Host, c.Port, c.User, c.Password, c.DBName, c.SSLMode)
}

// GetRedisAddr 获取Redis地址
func (c *RedisConfig) GetRedisAddr() string {
	return fmt.Sprintf("%s:%d", c.Host, c.Port)
}
