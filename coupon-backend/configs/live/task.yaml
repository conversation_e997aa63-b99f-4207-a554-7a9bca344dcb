# 生产环境 - 定时任务配置
database:
  host: localhost
  port: 5432
  user: postgres
  password: your_production_password
  db_name: coupon_db
  ssl_mode: disable
  max_idle_conns: 10
  max_open_conns: 50
  max_lifetime: 3600s

redis:
  host: localhost
  port: 6379
  password: your_redis_password
  db: 0
  pool_size: 10
  min_idle_conns: 5
  dial_timeout: 5s
  read_timeout: 3s
  write_timeout: 3s

log:
  level: info
  filename: logs/task.log
  max_size: 100
  max_age: 30
  compress: true

# 任务调度配置
task:
  # 商家同步任务 - 每天凌晨2点
  brand_sync_cron: "0 2 * * *"
  # 优惠券同步任务 - 每2小时
  coupon_sync_cron: "0 */2 * * *"
  # 优惠活动同步任务 - 每3小时
  deal_sync_cron: "0 */3 * * *"
  # 任务超时时间
  task_timeout: 3600s  # 60分钟