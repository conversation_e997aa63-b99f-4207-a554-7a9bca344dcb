package main

import (
	"context"
	"coupon-backend/infra/container"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
)

func main() {
	// Create dependency injection container
	c := container.NewContainer()

	// Set Gin mode
	gin.SetMode(c.Config.Server.Mode)

	// Create HTTP server
	server := &http.Server{
		Addr:         fmt.Sprintf(":%d", c.Config.Server.Port),
		Handler:      c.Router,
		ReadTimeout:  c.Config.Server.ReadTimeout,
		WriteTimeout: c.Config.Server.WriteTimeout,
	}

	// Start server
	go func() {
		log.Printf("🚀 API Server starting on port %d", c.Config.Server.Port)
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("❌ Failed to start server: %v", err)
		}
	}()

	// Wait for interrupt signal to gracefully shutdown the server
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM, syscall.SIGHUP)

	sig := <-quit
	log.Printf("🛑 Received signal: %v, shutting down API server...", sig)

	// Start graceful shutdown process
	gracefulShutdown(server, c)
}

// gracefulShutdown performs graceful shutdown
func gracefulShutdown(server *http.Server, container *container.Container) {
	// Create shutdown context, give server 30 seconds to finish existing requests
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	log.Println("📋 Starting graceful shutdown process...")

	// 1. Stop accepting new requests, wait for existing requests to complete
	log.Println("🔄 Stopping HTTP server...")
	if err := server.Shutdown(ctx); err != nil {
		log.Printf("⚠️ Server shutdown timeout, forcing close: %v", err)
		server.Close()
	} else {
		log.Println("✅ HTTP server stopped gracefully")
	}

	// 2. Clean up container resources (database connections, cache, goroutines, etc.)
	log.Println("🧹 Cleaning up container resources...")
	container.Cleanup()

	log.Println("✅ API Server shutdown completed")
}
