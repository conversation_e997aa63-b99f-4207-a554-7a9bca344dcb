package main

import (
	"coupon-backend/config"
	"coupon-backend/domain/task/service"
	"coupon-backend/infra/cache"
	"coupon-backend/infra/persistence"
	"coupon-backend/interfaces/task/scheduler"
	"log"
	"os"
	"os/signal"
	"syscall"
	"time"
)

func main() {
	// Load task configuration
	cfg := config.LoadConfigForService("task")

	// Initialize database
	if err := persistence.InitDatabase(&cfg.Database); err != nil {
		log.Fatalf("❌ Failed to initialize database: %v", err)
	}

	// Initialize Redis
	if err := persistence.InitRedis(&cfg.Redis); err != nil {
		log.Fatalf("❌ Failed to initialize redis: %v", err)
	}

	// Create cache service
	redisClient := persistence.GetRedis()
	cacheService := cache.NewCacheService(redisClient)

	// Create repositories
	db := persistence.GetDB()
	brandRepo := persistence.NewBrandRepository(db)
	couponRepo := persistence.NewCouponRepository(db)
	dealRepo := persistence.NewDealRepository(db)

	// Create task service
	taskService := service.NewTaskService(cacheService, brandRepo, couponRepo, dealRepo)

	// Create scheduler
	taskScheduler := scheduler.NewScheduler(taskService)

	// Start scheduler
	go taskScheduler.Start()

	log.Println("🚀 Task Scheduler started successfully")

	// Wait for interrupt signal
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM, syscall.SIGHUP)

	sig := <-quit
	log.Printf("🛑 Received signal: %v, shutting down Task Scheduler...", sig)

	// Start graceful shutdown process
	gracefulShutdown(taskScheduler)
}

// gracefulShutdown performs graceful shutdown
func gracefulShutdown(taskScheduler *scheduler.Scheduler) {
	log.Println("📋 Starting graceful shutdown process...")

	// 1. Stop scheduler (wait for current tasks to complete)
	log.Println("🔄 Stopping task scheduler...")
	taskScheduler.Stop()
	log.Println("✅ Task scheduler stopped")

	// 2. Wait for a while to ensure all tasks complete
	log.Println("⏳ Waiting for running tasks to complete...")
	time.Sleep(5 * time.Second)

	// 3. Close database connections
	log.Println("🗄️ Closing database connections...")
	if err := persistence.CloseDatabase(); err != nil {
		log.Printf("⚠️ Error closing database: %v", err)
	} else {
		log.Println("✅ Database connections closed")
	}

	// 4. Close Redis connections
	log.Println("🔴 Closing Redis connections...")
	if err := persistence.CloseRedis(); err != nil {
		log.Printf("⚠️ Error closing Redis: %v", err)
	} else {
		log.Println("✅ Redis connections closed")
	}

	log.Println("✅ Task Scheduler shutdown completed")
}
