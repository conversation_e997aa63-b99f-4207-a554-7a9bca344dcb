package appservice

import (
	"coupon-backend/application/category/dto"
	"coupon-backend/domain/category/service"
	"coupon-backend/infra/constant"
	"coupon-backend/infra/ecode"
	"github.com/gin-gonic/gin"
)

type CategoryAppService interface {
	GetCategoryDetailById(ctx *gin.Context, id uint64) (*dto.CategoryDetailResp, *ecode.Error)
	GetCategoryDetailBySlug(ctx *gin.Context, slug string) (*dto.CategoryDetailResp, *ecode.Error)
	GetCategoryList(ctx *gin.Context, req *dto.GetCategoryListReq) (*dto.CategoryListResp, *ecode.Error)
	GetCategoryCount(ctx *gin.Context) (int64, *ecode.Error)
}

type CategoryAppServiceImpl struct {
	categoryService service.CategoryService
}

func NewCategoryAppService(categoryService service.CategoryService) CategoryAppService {
	return &CategoryAppServiceImpl{
		categoryService: categoryService,
	}
}

// GetCategoryDetailById 根据ID获取分类详情
func (app *CategoryAppServiceImpl) GetCategoryDetailById(ctx *gin.Context, id uint64) (*dto.CategoryDetailResp, *ecode.Error) {
	if id == 0 {
		return nil, ecode.ErrInvalidID
	}

	category, err := app.categoryService.GetCategoryDetailById(ctx, id)
	if err != nil {
		return nil, err
	}

	resp := dto.Entity2DtoCategoryDetailResp(category)
	return resp, nil
}

// GetCategoryDetailBySlug 根据Slug获取分类详情
func (app *CategoryAppServiceImpl) GetCategoryDetailBySlug(ctx *gin.Context, slug string) (*dto.CategoryDetailResp, *ecode.Error) {
	if slug == "" {
		return nil, ecode.ErrInvalidParameter
	}

	category, err := app.categoryService.GetCategoryDetailBySlug(ctx, slug)
	if err != nil {
		return nil, err
	}

	resp := dto.Entity2DtoCategoryDetailResp(category)
	return resp, nil
}

// GetCategoryList 获取分类列表
func (app *CategoryAppServiceImpl) GetCategoryList(ctx *gin.Context, req *dto.GetCategoryListReq) (*dto.CategoryListResp, *ecode.Error) {
	// 参数校验
	if req.Page <= 0 {
		req.Page = constant.DefaultPage
	}
	if req.PageSize <= 0 {
		req.PageSize = constant.DefaultPageSize
	}
	if req.PageSize > constant.MaxPageSize {
		req.PageSize = constant.MaxPageSize
	}

	condition := req.Dto2ConditionGetCategoryList()

	categories, total, err := app.categoryService.GetCategoryListByCondition(ctx, condition)
	if err != nil {
		return nil, err
	}

	resp := dto.Entity2DtoCategoryListResp(req.PageSize, req.Page, total, categories)
	return resp, nil
}

// GetCategoryCount 获取分类总数
func (app *CategoryAppServiceImpl) GetCategoryCount(ctx *gin.Context) (int64, *ecode.Error) {
	return app.categoryService.GetCategoryCount(ctx)
}
