package subscription

import (
	"context"
	"coupon-backend/domain/subscription/entity"
	"coupon-backend/domain/subscription/service"
	"coupon-backend/interfaces/api/dto"
)

// SubscriptionApp 订阅应用服务
type SubscriptionApp struct {
	subscriptionService service.SubscriptionService
}

// NewSubscriptionApp 创建订阅应用服务
func NewSubscriptionApp(subscriptionService service.SubscriptionService) *SubscriptionApp {
	return &SubscriptionApp{
		subscriptionService: subscriptionService,
	}
}

// Subscribe 订阅邮箱
func (app *SubscriptionApp) Subscribe(ctx context.Context, req *dto.SubscribeReq) error {
	return app.subscriptionService.Subscribe(ctx, req.Email, req.Source, req.IPAddress, req.UserAgent)
}

// Unsubscribe 取消订阅
func (app *SubscriptionApp) Unsubscribe(ctx context.Context, email string) error {
	return app.subscriptionService.Unsubscribe(ctx, email)
}

// GetSubscription 获取订阅信息
func (app *SubscriptionApp) GetSubscription(ctx context.Context, email string) (*entity.Subscription, error) {
	return app.subscriptionService.GetSubscription(ctx, email)
}

// ListSubscriptions 获取订阅列表
func (app *SubscriptionApp) ListSubscriptions(ctx context.Context, req *dto.SubscriptionListReq) (*dto.SubscriptionListResp, error) {
	condition := make(map[string]interface{})

	if req.Status != nil {
		condition["status"] = *req.Status
	}
	if req.Email != "" {
		condition["email"] = req.Email
	}
	if req.Source != "" {
		condition["source"] = req.Source
	}
	if req.Page > 0 {
		condition["page"] = req.Page
	}
	if req.PageSize > 0 {
		condition["page_size"] = req.PageSize
	}

	subscriptions, total, err := app.subscriptionService.ListSubscriptions(ctx, condition)
	if err != nil {
		return nil, err
	}

	// 转换为响应DTO
	var items []*dto.SubscriptionItem
	for _, sub := range subscriptions {
		items = append(items, &dto.SubscriptionItem{
			ID:        sub.ID,
			Email:     sub.Email,
			Status:    sub.Status,
			Source:    sub.Source,
			CreatedAt: sub.CreatedAt,
			UpdatedAt: sub.UpdatedAt,
		})
	}

	return &dto.SubscriptionListResp{
		Items:    items,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}, nil
}
