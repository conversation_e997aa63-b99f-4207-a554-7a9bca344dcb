package appservice

import (
	"coupon-backend/application/brand/dto"
	"coupon-backend/domain/brand/service"
	categoryEntity "coupon-backend/domain/category/entity"
	categoryService "coupon-backend/domain/category/service"
	couponService "coupon-backend/domain/coupon/service"
	dealService "coupon-backend/domain/deal/service"
	"coupon-backend/infra/constant"
	"coupon-backend/infra/ecode"

	"github.com/gin-gonic/gin"
)

type BrandAppService interface {
	GetBrandDetailById(ctx *gin.Context, id uint64) (*dto.BrandDetailResp, *ecode.Error)
	GetBrandDetailByUniqueName(ctx *gin.Context, uniqueName string) (*dto.BrandDetailResp, *ecode.Error)
	GetBrandList(ctx *gin.Context, req *dto.GetBrandListReq) (*dto.BrandListResp, *ecode.Error)
	GetBrandCount(ctx *gin.Context) (int64, *ecode.Error)
}

type BrandAppServiceImpl struct {
	brandService    service.BrandService
	categoryService categoryService.CategoryService
	couponService   couponService.CouponService
	dealService     dealService.DealService
}

func NewBrandAppService(brandService service.BrandService, categoryService categoryService.CategoryService, couponService couponService.CouponService, dealService dealService.DealService) BrandAppService {
	return &BrandAppServiceImpl{
		brandService:    brandService,
		categoryService: categoryService,
		couponService:   couponService,
		dealService:     dealService,
	}
}

// GetBrandDetailById 根据ID获取商家详情
func (app *BrandAppServiceImpl) GetBrandDetailById(ctx *gin.Context, id uint64) (*dto.BrandDetailResp, *ecode.Error) {
	if id == 0 {
		return nil, ecode.ErrInvalidID
	}

	brand, err := app.brandService.GetBrandDetailById(ctx, id)
	if err != nil {
		return nil, err
	}

	// 获取分类信息
	var category *categoryEntity.Category
	if brand.CategoryID > 0 {
		category, _ = app.categoryService.GetCategoryDetailById(ctx, uint64(brand.CategoryID))
	}

	// 获取该品牌的优惠券和优惠活动
	coupons, deals, err := app.getBrandCouponsAndDeals(ctx, uint64(brand.ID))
	if err != nil {
		return nil, err
	}

	resp := dto.Entity2DtoBrandDetailRespWithCouponsDeals(brand, category, coupons, deals)
	return resp, nil
}

// GetBrandDetailByUniqueName 根据UniqueName获取商家详情
func (app *BrandAppServiceImpl) GetBrandDetailByUniqueName(ctx *gin.Context, uniqueName string) (*dto.BrandDetailResp, *ecode.Error) {
	if uniqueName == "" {
		return nil, ecode.ErrInvalidParameter
	}

	brand, err := app.brandService.GetBrandDetailByUniqueName(ctx, uniqueName)
	if err != nil {
		return nil, err
	}

	// 获取分类信息
	var category *categoryEntity.Category
	if brand.CategoryID > 0 {
		category, _ = app.categoryService.GetCategoryDetailById(ctx, uint64(brand.CategoryID))
	}

	// 获取该品牌的优惠券和优惠活动
	coupons, deals, err := app.getBrandCouponsAndDeals(ctx, uint64(brand.ID))
	if err != nil {
		return nil, err
	}

	resp := dto.Entity2DtoBrandDetailRespWithCouponsDeals(brand, category, coupons, deals)
	return resp, nil
}

// GetBrandList 获取商家列表
func (app *BrandAppServiceImpl) GetBrandList(ctx *gin.Context, req *dto.GetBrandListReq) (*dto.BrandListResp, *ecode.Error) {
	// 参数校验
	if req.Page <= 0 {
		req.Page = constant.DefaultPage
	}
	if req.PageSize <= 0 {
		req.PageSize = constant.DefaultPageSize
	}
	if req.PageSize > constant.MaxPageSize {
		req.PageSize = constant.MaxPageSize
	}

	condition := req.Dto2ConditionGetBrandList()
	// 添加状态条件来过滤掉已下线商家，用户通过API访问的应该只看到上线的商家
	condition["status"] = constant.BrandStatusEnabled

	brands, total, err := app.brandService.GetBrandListByCondition(ctx, condition)
	if err != nil {
		return nil, err
	}

	// 高效获取分类信息 - 只获取需要的分类
	categoryIDs := make([]uint64, 0, len(brands))
	brandIDs := make([]uint64, 0, len(brands))
	for _, brand := range brands {
		brandIDs = append(brandIDs, uint64(brand.ID))
		if brand.CategoryID > 0 {
			categoryIDs = append(categoryIDs, uint64(brand.CategoryID))
		}
	}

	// 使用domain service的批量获取方法（包含缓存）
	categories, err := app.categoryService.GetCategoryListByIDs(ctx, categoryIDs)
	if err != nil {
		return nil, err
	}

	categoryMap := make(map[uint64]*categoryEntity.Category)
	for _, category := range categories {
		categoryMap[uint64(category.ID)] = category
	}

	// 批量获取所有品牌的优惠券和优惠活动
	couponEntities, err := app.couponService.GetCouponListByBrandIDs(ctx, brandIDs)
	if err != nil {
		return nil, err
	}

	dealEntities, err := app.dealService.GetDealListByBrandIDs(ctx, brandIDs)
	if err != nil {
		return nil, err
	}

	// 按品牌ID分组优惠券和优惠活动
	brandCouponsMap := make(map[uint64][]*dto.BrandCouponResp)
	brandDealsMap := make(map[uint64][]*dto.BrandDealResp)

	// 分组优惠券
	for _, coupon := range couponEntities {
		brandID := uint64(coupon.BrandID)
		couponResp := &dto.BrandCouponResp{
			ID:          coupon.ID,
			CreatedAt:   coupon.CreatedAt,
			UpdatedAt:   coupon.UpdatedAt,
			BrandID:     coupon.BrandID,
			CategoryID:  coupon.CategoryID,
			Code:        coupon.Code,
			IsFeatured:  coupon.IsFeatured,
			IsExclusive: coupon.IsExclusive,
			Discount:    coupon.Discount,
			Name:        coupon.Name,
			Description: coupon.Description,
			StartDate:   coupon.StartDate,
			EndDate:     coupon.EndDate,
			Status:      coupon.Status,
		}
		brandCouponsMap[brandID] = append(brandCouponsMap[brandID], couponResp)
	}

	// 分组优惠活动
	for _, deal := range dealEntities {
		brandID := uint64(deal.BrandID)
		dealResp := &dto.BrandDealResp{
			ID:          deal.ID,
			CreatedAt:   deal.CreatedAt,
			UpdatedAt:   deal.UpdatedAt,
			BrandID:     deal.BrandID,
			CategoryID:  deal.CategoryID,
			Code:        deal.Code,
			Title:       deal.Title,
			Description: deal.Description,
			Img:         deal.Img,
			IsHotDeal:   deal.IsHotDeal,
			IsFeatured:  deal.IsFeatured,
			Discount:    deal.Discount,
			OriginURL:   deal.OriginURL,
			TrackingUrl: deal.TrackingUrl,
			StartDate:   deal.StartDate,
			EndDate:     deal.EndDate,
			Status:      deal.Status,
		}
		brandDealsMap[brandID] = append(brandDealsMap[brandID], dealResp)
	}

	// 构建响应
	resp := &dto.BrandListResp{
		Total:     total,
		PageSize:  req.PageSize,
		Page:      req.Page,
		BrandList: make([]*dto.BrandDetailResp, 0, len(brands)),
	}

	for _, brand := range brands {
		brandID := uint64(brand.ID)
		var category *categoryEntity.Category
		if brand.CategoryID > 0 {
			if c, ok := categoryMap[uint64(brand.CategoryID)]; ok {
				category = c
			}
		}

		coupons := brandCouponsMap[brandID]
		deals := brandDealsMap[brandID]
		if coupons == nil {
			coupons = []*dto.BrandCouponResp{}
		}
		if deals == nil {
			deals = []*dto.BrandDealResp{}
		}

		brandResp := dto.Entity2DtoBrandDetailRespWithCouponsDeals(brand, category, coupons, deals)
		resp.BrandList = append(resp.BrandList, brandResp)
	}

	return resp, nil
}

// GetBrandCount 获取商家总数
func (app *BrandAppServiceImpl) GetBrandCount(ctx *gin.Context) (int64, *ecode.Error) {
	return app.brandService.GetBrandCount(ctx)
}

// getBrandCouponsAndDeals 获取品牌对应的优惠券和优惠活动
func (app *BrandAppServiceImpl) getBrandCouponsAndDeals(ctx *gin.Context, brandID uint64) ([]*dto.BrandCouponResp, []*dto.BrandDealResp, *ecode.Error) {
	var coupons []*dto.BrandCouponResp
	var deals []*dto.BrandDealResp
	
	// 获取该品牌的优惠券
	couponEntities, err := app.couponService.GetCouponListByBrandIDs(ctx, []uint64{brandID})
	if err != nil {
		return nil, nil, err
	}
	
	// 获取该品牌的优惠活动
	dealEntities, err := app.dealService.GetDealListByBrandIDs(ctx, []uint64{brandID})
	if err != nil {
		return nil, nil, err
	}
	
	// 转换优惠券为BrandCouponResp
	coupons = make([]*dto.BrandCouponResp, 0, len(couponEntities))
	for _, coupon := range couponEntities {
		couponResp := &dto.BrandCouponResp{
			ID:          coupon.ID,
			CreatedAt:   coupon.CreatedAt,
			UpdatedAt:   coupon.UpdatedAt,
			BrandID:     coupon.BrandID,
			CategoryID:  coupon.CategoryID,
			Code:        coupon.Code,
			IsFeatured:  coupon.IsFeatured,
			IsExclusive: coupon.IsExclusive,
			Discount:    coupon.Discount,
			Name:        coupon.Name,
			Description: coupon.Description,
			StartDate:   coupon.StartDate,
			EndDate:     coupon.EndDate,
			Status:      coupon.Status,
		}
		coupons = append(coupons, couponResp)
	}
	
	// 转换优惠活动为BrandDealResp
	deals = make([]*dto.BrandDealResp, 0, len(dealEntities))
	for _, deal := range dealEntities {
		dealResp := &dto.BrandDealResp{
			ID:          deal.ID,
			CreatedAt:   deal.CreatedAt,
			UpdatedAt:   deal.UpdatedAt,
			BrandID:     deal.BrandID,
			CategoryID:  deal.CategoryID,
			Code:        deal.Code,
			Title:       deal.Title,
			Description: deal.Description,
			Img:         deal.Img,
			IsHotDeal:   deal.IsHotDeal,
			IsFeatured:  deal.IsFeatured,
			Discount:    deal.Discount,
			OriginURL:   deal.OriginURL,
			TrackingUrl: deal.TrackingUrl,
			StartDate:   deal.StartDate,
			EndDate:     deal.EndDate,
			Status:      deal.Status,
		}
		deals = append(deals, dealResp)
	}
	
	return coupons, deals, nil
}
