package dto

import (
	brandDto "coupon-backend/application/brand/dto"
	categoryDto "coupon-backend/application/category/dto"
	couponDto "coupon-backend/application/coupon/dto"
	dealDto "coupon-backend/application/deal/dto"
	"coupon-backend/domain/article/entity"
	brandEntity "coupon-backend/domain/brand/entity"
	categoryEntity "coupon-backend/domain/category/entity"
	couponEntity "coupon-backend/domain/coupon/entity"
	dealEntity "coupon-backend/domain/deal/entity"
	"time"
)

// GetArticleListReq 获取文章列表请求
type GetArticleListReq struct {
	Page       int    `json:"page" form:"page"`
	PageSize   int    `json:"page_size" form:"page_size"`
	Search     string `json:"search" form:"search"`
	Sort       string `json:"sort" form:"sort"`
	Featured   bool   `json:"featured" form:"featured"`
	Published  bool   `json:"published" form:"published"`
	BrandID    uint64 `json:"brand_id" form:"brand_id"`
	CategoryID uint64 `json:"category_id" form:"category_id"`
}

// ArticleDetailResp 文章详情响应
type ArticleDetailResp struct {
	ID            uint64                          `json:"id"`
	CreatedAt     time.Time                       `json:"created_at"`
	UpdatedAt     time.Time                       `json:"updated_at"`
	PublishedAt   time.Time                       `json:"published_at"`
	Slug          string                          `json:"slug"`
	Title         string                          `json:"title"`
	Description   string                          `json:"description"`
	Content       string                          `json:"content"`
	FeaturedImage string                          `json:"featured_image"`
	MetaTitle     string                          `json:"meta_title"`
	MetaDesc      string                          `json:"meta_description"`
	AuthorName    string                          `json:"author_name"`
	Featured      bool                            `json:"featured"`
	Published     bool                            `json:"published"`
	BrandID       uint64                          `json:"brand_id"`
	CategoryID    uint64                          `json:"category_id"`
	Brand         *brandDto.BrandDetailResp       `json:"brand"`
	Category      *categoryDto.CategoryDetailResp `json:"category"`
	// 仅在详情接口返回
	Coupons []*couponDto.CouponDetailResp `json:"coupons,omitempty"`
	Deals   []*dealDto.DealDetailResp     `json:"deals,omitempty"`
}

// ArticleListResp 文章列表响应
type ArticleListResp struct {
	Total       int64                `json:"total"`
	Page        int                  `json:"page"`
	PageSize    int                  `json:"page_size"`
	ArticleList []*ArticleDetailResp `json:"article_list"`
}

// Dto2ConditionGetArticleList 获取文章列表请求转换为 condition
func (req *GetArticleListReq) Dto2ConditionGetArticleList() (condition map[string]interface{}) {
	condition = map[string]interface{}{}
	// 分页处理
	offset := (req.Page - 1) * req.PageSize
	condition["offset"] = offset
	if req.Page > 0 {
		condition["page"] = req.Page
	}
	if req.PageSize > 0 {
		condition["page_size"] = req.PageSize
	}
	if req.Search != "" {
		condition["search"] = req.Search
	}
	if req.Sort != "" {
		condition["sort"] = req.Sort
	}
	if req.Featured {
		condition["featured"] = req.Featured
	}
	if req.Published {
		condition["published"] = req.Published
	}
	if req.BrandID > 0 {
		condition["brand_id"] = req.BrandID
	}
	if req.CategoryID > 0 {
		condition["category_id"] = req.CategoryID
	}
	return condition
}

// Entity2DtoArticleDetailResp 实体转换为DTO（列表用）
func Entity2DtoArticleDetailResp(article *entity.Article, brand *brandEntity.Brand, brandCategory *categoryEntity.Category, category *categoryEntity.Category) *ArticleDetailResp {
	if article == nil {
		return &ArticleDetailResp{}
	}

	resp := &ArticleDetailResp{
		ID:            article.Id,
		CreatedAt:     article.CreatedAt,
		UpdatedAt:     article.UpdatedAt,
		PublishedAt:   article.PublishedAt,
		Slug:          article.Slug,
		Title:         article.Title,
		Description:   article.Description,
		Content:       article.Content,
		FeaturedImage: article.FeaturedImage,
		MetaTitle:     article.MetaTitle,
		MetaDesc:      article.MetaDesc,
		AuthorName:    article.AuthorName,
		Featured:      article.Featured,
		Published:     article.Published,
		BrandID:       article.BrandId,
		CategoryID:    article.CategoryId,
	}

	// 转换商家信息
	if brand != nil {
		resp.Brand = brandDto.Entity2DtoBrandDetailResp(brand, brandCategory)
	} else {
		resp.Brand = &brandDto.BrandDetailResp{}
	}

	// 转换分类信息
	if category != nil {
		resp.Category = categoryDto.Entity2DtoCategoryDetailResp(category)
	} else {
		resp.Category = &categoryDto.CategoryDetailResp{}
	}

	return resp
}

// Entity2DtoArticleDetailRespWithRelated 实体转换为DTO（详情用，包含关联数据）
func Entity2DtoArticleDetailRespWithRelated(
	article *entity.Article,
	brand *brandEntity.Brand,
	brandCategory *categoryEntity.Category,
	category *categoryEntity.Category,
	coupons []*couponEntity.Coupon,
	deals []*dealEntity.Deal,
	couponBrandMap map[uint64]*brandEntity.Brand,
	dealBrandMap map[uint64]*brandEntity.Brand,
	categoryMap map[uint64]*categoryEntity.Category,
) *ArticleDetailResp {
	resp := Entity2DtoArticleDetailResp(article, brand, brandCategory, category)

	// 转换优惠券数据
	if len(coupons) > 0 {
		resp.Coupons = make([]*couponDto.CouponDetailResp, 0, len(coupons))
		for _, coupon := range coupons {
			var couponBrand *brandEntity.Brand
			var couponBrandCategory *categoryEntity.Category
			var couponCategory *categoryEntity.Category

			if cb, ok := couponBrandMap[uint64(coupon.BrandID)]; ok {
				couponBrand = cb
				if couponBrand.CategoryID > 0 {
					if cbc, ok := categoryMap[uint64(couponBrand.CategoryID)]; ok {
						couponBrandCategory = cbc
					}
				}
			}

			if coupon.CategoryID > 0 {
				if cc, ok := categoryMap[uint64(coupon.CategoryID)]; ok {
					couponCategory = cc
				}
			}

			couponResp := couponDto.Entity2DtoCouponDetailRespWithBrandCategory(coupon, couponBrand, couponBrandCategory, couponCategory)
			resp.Coupons = append(resp.Coupons, couponResp)
		}
	}

	// 转换优惠活动数据
	if len(deals) > 0 {
		resp.Deals = make([]*dealDto.DealDetailResp, 0, len(deals))
		for _, deal := range deals {
			var dealBrand *brandEntity.Brand
			var dealCategory *categoryEntity.Category

			if db, ok := dealBrandMap[uint64(deal.BrandID)]; ok {
				dealBrand = db
			}

			if deal.CategoryID > 0 {
				if dc, ok := categoryMap[uint64(deal.CategoryID)]; ok {
					dealCategory = dc
				}
			}

			dealResp := dealDto.Entity2DtoDealDetailResp(deal, dealBrand, dealCategory)
			resp.Deals = append(resp.Deals, dealResp)
		}
	}

	return resp
}

// Entity2DtoArticleListResp 实体列表转换为DTO
func Entity2DtoArticleListResp(
	pageSize int,
	page int,
	total int64,
	articleList []*entity.Article,
	brandMap map[uint64]*brandEntity.Brand,
	categoryMap map[uint64]*categoryEntity.Category,
) *ArticleListResp {
	resp := &ArticleListResp{
		Total:    total,
		PageSize: pageSize,
		Page:     page,
	}

	for _, article := range articleList {
		var brand *brandEntity.Brand
		var brandCategory *categoryEntity.Category
		var category *categoryEntity.Category

		if b, ok := brandMap[uint64(article.BrandId)]; ok {
			brand = b
			// 获取商家的分类信息
			if brand.CategoryID > 0 {
				if bc, ok := categoryMap[uint64(brand.CategoryID)]; ok {
					brandCategory = bc
				}
			}
		}

		if article.CategoryId > 0 {
			if c, ok := categoryMap[uint64(article.CategoryId)]; ok {
				category = c
			}
		}

		articleResp := Entity2DtoArticleDetailResp(article, brand, brandCategory, category)
		resp.ArticleList = append(resp.ArticleList, articleResp)
	}

	return resp
}
