package appservice

import (
	"coupon-backend/application/article/dto"
	"coupon-backend/domain/article/entity"
	"coupon-backend/domain/article/service"
	brandEntity "coupon-backend/domain/brand/entity"
	brandService "coupon-backend/domain/brand/service"
	categoryEntity "coupon-backend/domain/category/entity"
	categoryService "coupon-backend/domain/category/service"
	couponEntity "coupon-backend/domain/coupon/entity"
	couponService "coupon-backend/domain/coupon/service"
	dealEntity "coupon-backend/domain/deal/entity"
	dealService "coupon-backend/domain/deal/service"
	"coupon-backend/infra/constant"
	"coupon-backend/infra/ecode"

	"github.com/gin-gonic/gin"
)

type ArticleAppService interface {
	GetArticleDetailById(ctx *gin.Context, id uint64) (*dto.ArticleDetailResp, *ecode.Error)
	GetArticleDetailBySlug(ctx *gin.Context, slug string) (*dto.ArticleDetailResp, *ecode.Error)
	GetArticleList(ctx *gin.Context, req *dto.GetArticleListReq) (*dto.ArticleListResp, *ecode.Error)
	GetArticleCount(ctx *gin.Context) (int64, *ecode.Error)
}

type ArticleAppServiceImpl struct {
	articleService  service.ArticleService
	brandService    brandService.BrandService
	categoryService categoryService.CategoryService
	couponService   couponService.CouponService
	dealService     dealService.DealService
}

func NewArticleAppService(
	articleService service.ArticleService,
	brandService brandService.BrandService,
	categoryService categoryService.CategoryService,
	couponService couponService.CouponService,
	dealService dealService.DealService,
) ArticleAppService {
	return &ArticleAppServiceImpl{
		articleService:  articleService,
		brandService:    brandService,
		categoryService: categoryService,
		couponService:   couponService,
		dealService:     dealService,
	}
}

// GetArticleDetailById 根据ID获取文章详情
func (app *ArticleAppServiceImpl) GetArticleDetailById(ctx *gin.Context, id uint64) (*dto.ArticleDetailResp, *ecode.Error) {
	if id == 0 {
		return nil, ecode.ErrInvalidID
	}

	article, err := app.articleService.GetArticleDetailById(ctx, id)
	if err != nil {
		return nil, err
	}

	return app.buildArticleDetailResp(ctx, article, true)
}

// GetArticleDetailBySlug 根据Slug获取文章详情
func (app *ArticleAppServiceImpl) GetArticleDetailBySlug(ctx *gin.Context, slug string) (*dto.ArticleDetailResp, *ecode.Error) {
	if slug == "" {
		return nil, ecode.ErrInvalidParameter
	}

	article, err := app.articleService.GetArticleDetailBySlug(ctx, slug)
	if err != nil {
		return nil, err
	}

	return app.buildArticleDetailResp(ctx, article, true)
}

// GetArticleList 获取文章列表
func (app *ArticleAppServiceImpl) GetArticleList(ctx *gin.Context, req *dto.GetArticleListReq) (*dto.ArticleListResp, *ecode.Error) {
	// 参数校验
	if req.Page <= 0 {
		req.Page = constant.DefaultPage
	}
	if req.PageSize <= 0 {
		req.PageSize = constant.DefaultPageSize
	}
	if req.PageSize > constant.MaxPageSize {
		req.PageSize = constant.MaxPageSize
	}

	condition := req.Dto2ConditionGetArticleList()
	// 默认只返回已发布的文章
	if !req.Published {
		condition["published"] = true
	}

	articles, total, err := app.articleService.GetArticleListByCondition(ctx, condition)
	if err != nil {
		return nil, err
	}

	// 获取关联数据
	brandIDs := make([]uint64, 0, len(articles))
	categoryIDs := make([]uint64, 0, len(articles))
	for _, article := range articles {
		if article.BrandId > 0 {
			brandIDs = append(brandIDs, article.BrandId)
		}
		if article.CategoryId > 0 {
			categoryIDs = append(categoryIDs, article.CategoryId)
		}
	}

	// 获取商家信息
	brands, err := app.brandService.GetBrandListByIDs(ctx, brandIDs)
	if err != nil {
		return nil, err
	}

	brandMap := make(map[uint64]*brandEntity.Brand)
	brandCategoryIDs := make([]uint64, 0)
	for _, brand := range brands {
		brandMap[uint64(brand.ID)] = brand
		if brand.CategoryID > 0 {
			brandCategoryIDs = append(brandCategoryIDs, uint64(brand.CategoryID))
		}
	}

	// 合并所有分类ID
	allCategoryIDs := append(categoryIDs, brandCategoryIDs...)

	// 获取分类信息
	categories, err := app.categoryService.GetCategoryListByIDs(ctx, allCategoryIDs)
	if err != nil {
		return nil, err
	}

	categoryMap := make(map[uint64]*categoryEntity.Category)
	for _, category := range categories {
		categoryMap[uint64(category.ID)] = category
	}

	resp := dto.Entity2DtoArticleListResp(req.PageSize, req.Page, total, articles, brandMap, categoryMap)
	return resp, nil
}

// GetArticleCount 获取文章总数
func (app *ArticleAppServiceImpl) GetArticleCount(ctx *gin.Context) (int64, *ecode.Error) {
	return app.articleService.GetArticleCount(ctx)
}

// buildArticleDetailResp 构建文章详情响应（包含关联数据）
func (app *ArticleAppServiceImpl) buildArticleDetailResp(ctx *gin.Context, article *entity.Article, includeRelated bool) (*dto.ArticleDetailResp, *ecode.Error) {
	var brand *brandEntity.Brand
	var brandCategory *categoryEntity.Category
	var category *categoryEntity.Category
	var coupons []*couponEntity.Coupon
	var deals []*dealEntity.Deal

	// 获取商家信息
	if article.BrandId > 0 {
		brand, _ = app.brandService.GetBrandDetailById(ctx, article.BrandId)
		// 获取商家的分类信息
		if brand != nil && brand.CategoryID > 0 {
			brandCategory, _ = app.categoryService.GetCategoryDetailById(ctx, uint64(brand.CategoryID))
		}
	}

	// 获取分类信息
	if article.CategoryId > 0 {
		category, _ = app.categoryService.GetCategoryDetailById(ctx, article.CategoryId)
	}

	// 如果需要包含关联数据（详情接口）
	if includeRelated && article.BrandId > 0 {
		// 获取该商家的优惠券
		coupons, _ = app.couponService.GetCouponListByBrandIDs(ctx, []uint64{article.BrandId})
		// 获取该商家的优惠活动
		deals, _ = app.dealService.GetDealListByBrandIDs(ctx, []uint64{article.BrandId})
	}

	// 构建关联数据的映射
	couponBrandMap := make(map[uint64]*brandEntity.Brand)
	dealBrandMap := make(map[uint64]*brandEntity.Brand)
	categoryMap := make(map[uint64]*categoryEntity.Category)

	if brand != nil {
		couponBrandMap[uint64(brand.ID)] = brand
		dealBrandMap[uint64(brand.ID)] = brand
	}
	if brandCategory != nil {
		categoryMap[uint64(brandCategory.ID)] = brandCategory
	}
	if category != nil {
		categoryMap[uint64(category.ID)] = category
	}

	if includeRelated {
		return dto.Entity2DtoArticleDetailRespWithRelated(
			article, brand, brandCategory, category,
			coupons, deals, couponBrandMap, dealBrandMap, categoryMap,
		), nil
	}

	return dto.Entity2DtoArticleDetailResp(article, brand, brandCategory, category), nil
}
