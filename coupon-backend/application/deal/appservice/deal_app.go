package appservice

import (
	"coupon-backend/application/deal/dto"
	brandEntity "coupon-backend/domain/brand/entity"
	brandService "coupon-backend/domain/brand/service"
	categoryEntity "coupon-backend/domain/category/entity"
	"coupon-backend/domain/deal/service"
	"coupon-backend/infra/constant"
	"coupon-backend/infra/ecode"

	"github.com/gin-gonic/gin"
)

type DealAppService interface {
	GetDealDetailById(ctx *gin.Context, id uint64) (*dto.DealDetailResp, *ecode.Error)
	GetDealList(ctx *gin.Context, req *dto.GetDealListReq) (*dto.DealListResp, *ecode.Error)
	GetDealCount(ctx *gin.Context) (int64, *ecode.Error)
}

type DealAppServiceImpl struct {
	dealService     service.DealService
	brandService    brandService.BrandService
	categoryService interface {
		GetCategoryDetailById(ctx *gin.Context, id uint64) (*categoryEntity.Category, *ecode.Error)
		GetCategoryListByIDs(ctx *gin.Context, ids []uint64) ([]*categoryEntity.Category, *ecode.Error)
	}
}

func NewDealAppService(dealService service.DealService, brandService brandService.BrandService, categoryService interface {
	GetCategoryDetailById(ctx *gin.Context, id uint64) (*categoryEntity.Category, *ecode.Error)
	GetCategoryListByIDs(ctx *gin.Context, ids []uint64) ([]*categoryEntity.Category, *ecode.Error)
}) DealAppService {
	return &DealAppServiceImpl{
		dealService:     dealService,
		brandService:    brandService,
		categoryService: categoryService,
	}
}

// GetDealDetailById 根据ID获取优惠活动详情
func (app *DealAppServiceImpl) GetDealDetailById(ctx *gin.Context, id uint64) (*dto.DealDetailResp, *ecode.Error) {
	if id == 0 {
		return nil, ecode.ErrInvalidID
	}

	deal, err := app.dealService.GetDealDetailById(ctx, id)
	if err != nil {
		return nil, err
	}

	// 获取商家信息
	var brand *brandEntity.Brand
	if deal.BrandID > 0 {
		brand, _ = app.brandService.GetBrandDetailById(ctx, uint64(deal.BrandID))
	}

	// 获取分类信息
	var category *categoryEntity.Category
	if deal.CategoryID > 0 {
		category, _ = app.categoryService.GetCategoryDetailById(ctx, uint64(deal.CategoryID))
	}

	resp := dto.Entity2DtoDealDetailResp(deal, brand, category)
	return resp, nil
}

// GetDealList 获取优惠活动列表
func (app *DealAppServiceImpl) GetDealList(ctx *gin.Context, req *dto.GetDealListReq) (*dto.DealListResp, *ecode.Error) {
	// 参数校验
	if req.Page <= 0 {
		req.Page = constant.DefaultPage
	}
	if req.PageSize <= 0 {
		req.PageSize = constant.DefaultPageSize
	}
	if req.PageSize > constant.MaxPageSize {
		req.PageSize = constant.MaxPageSize
	}

	condition := req.Dto2ConditionGetDealList()
	// 添加状态条件来过滤掉已下线优惠活动，用户通过API访问的应该只看到上线的优惠活动
	if req.Status == 0 {
		condition["status"] = constant.DealStatusEnabled
	}

	deals, total, err := app.dealService.GetDealListByCondition(ctx, condition)
	if err != nil {
		return nil, err
	}

	// 获取商家信息
	brandIDs := make([]uint64, 0, len(deals))
	categoryIDs := make([]uint64, 0, len(deals))
	for _, deal := range deals {
		if deal.BrandID > 0 {
			brandIDs = append(brandIDs, uint64(deal.BrandID))
		}
		if deal.CategoryID > 0 {
			categoryIDs = append(categoryIDs, uint64(deal.CategoryID))
		}
	}

	var brandMap map[uint64]*brandEntity.Brand
	if len(brandIDs) > 0 {
		brands, err := app.brandService.GetBrandListByIDs(ctx, brandIDs)
		if err != nil {
			return nil, err
		}
		brandMap = make(map[uint64]*brandEntity.Brand)
		for _, brand := range brands {
			brandMap[uint64(brand.ID)] = brand
		}
	} else {
		brandMap = make(map[uint64]*brandEntity.Brand)
	}

	// 获取分类信息
	var categoryMap map[uint64]*categoryEntity.Category
	if len(categoryIDs) > 0 {
		categories, err := app.categoryService.GetCategoryListByIDs(ctx, categoryIDs)
		if err != nil {
			return nil, err
		}
		categoryMap = make(map[uint64]*categoryEntity.Category)
		for _, category := range categories {
			categoryMap[uint64(category.ID)] = category
		}
	} else {
		categoryMap = make(map[uint64]*categoryEntity.Category)
	}

	resp := dto.Entity2DtoDealListResp(req.PageSize, req.Page, total, deals, brandMap, categoryMap)
	return resp, nil
}

// GetDealCount 获取优惠活动总数
func (app *DealAppServiceImpl) GetDealCount(ctx *gin.Context) (int64, *ecode.Error) {
	return app.dealService.GetDealCount(ctx)
}
