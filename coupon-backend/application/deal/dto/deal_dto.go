package dto

import (
	"coupon-backend/application/brand/dto"
	categoryDto "coupon-backend/application/category/dto"
	brandEntity "coupon-backend/domain/brand/entity"
	categoryEntity "coupon-backend/domain/category/entity"
	"coupon-backend/domain/deal/entity"
	"time"
)

type GetDealListReq struct {
	Page         int    `json:"page" form:"page"`
	PageSize     int    `json:"page_size" form:"page_size"`
	Search       string `json:"search" form:"search"`
	Sort         string `json:"sort" form:"sort"`
	BrandID      uint64 `json:"brand_id" form:"brand_id"`
	CategoryID   uint64 `json:"category_id" form:"category_id"`
	CategorySlug string `json:"category_slug" form:"category_slug"`
	IsHotDeal    bool   `json:"is_hot_deal" form:"is_hot_deal"`
	IsFeatured   bool   `json:"is_featured" form:"is_featured"`
	Status       int    `json:"status" form:"status"`
}

// 响应对象

type DealDetailResp struct {
	ID          uint                            `json:"id"`
	CreatedAt   time.Time                       `json:"created_at"`
	UpdatedAt   time.Time                       `json:"updated_at"`
	BrandID     uint                            `json:"brand_id"`
	Brand       *dto.BrandDetailResp            `json:"brand"` // 商家信息
	CategoryID  uint                            `json:"category_id"`
	Category    *categoryDto.CategoryDetailResp `json:"category"` // 分类信息
	Code        string                          `json:"code"`
	Title       string                          `json:"title"`
	Description string                          `json:"description"`
	Img         string                          `json:"img"`
	IsHotDeal   bool                            `json:"is_hot_deal"`
	IsFeatured  bool                            `json:"is_featured"`
	Discount    string                          `json:"discount"`
	OriginURL   string                          `json:"origin_url"`
	TrackingUrl string                          `json:"tracking_url"`
	StartDate   *time.Time                      `json:"start_date"`
	EndDate     *time.Time                      `json:"end_date"`
	Status      int                             `json:"status"`
}

type DealListResp struct {
	Total    int64             `json:"total"`
	Page     int               `json:"page"`
	PageSize int               `json:"page_size"`
	DealList []*DealDetailResp `json:"deal_list"`
}

// Dto2ConditionGetDealList 获取优惠活动列表请求转换为 condition
func (req *GetDealListReq) Dto2ConditionGetDealList() (condition map[string]interface{}) {
	condition = map[string]interface{}{}
	// 分页处理
	offset := (req.Page - 1) * req.PageSize
	condition["offset"] = offset
	if req.Page > 0 {
		condition["page"] = req.Page
	}
	if req.PageSize > 0 {
		condition["page_size"] = req.PageSize
	}
	if req.Search != "" {
		condition["search"] = req.Search
	}
	if req.Sort != "" {
		condition["sort"] = req.Sort
	}
	if req.BrandID > 0 {
		condition["brand_id"] = req.BrandID
	}
	if req.CategoryID > 0 {
		condition["category_id"] = req.CategoryID
	}
	if req.CategorySlug != "" {
		condition["category_slug"] = req.CategorySlug
	}
	if req.IsHotDeal {
		condition["is_hot_deal"] = req.IsHotDeal
	}
	if req.IsFeatured {
		condition["is_featured"] = req.IsFeatured
	}
	if req.Status > 0 {
		condition["status"] = req.Status
	}
	return condition
}

func Entity2DtoDealDetailResp(deal *entity.Deal, brand *brandEntity.Brand, category *categoryEntity.Category) (resp *DealDetailResp) {
	resp = &DealDetailResp{}
	resp.ID = deal.ID
	resp.BrandID = deal.BrandID
	resp.CategoryID = deal.CategoryID
	resp.Code = deal.Code
	resp.Title = deal.Title
	resp.Description = deal.Description
	resp.Img = deal.Img
	resp.IsHotDeal = deal.IsHotDeal
	resp.IsFeatured = deal.IsFeatured
	resp.Discount = deal.Discount
	resp.OriginURL = deal.OriginURL
	resp.TrackingUrl = deal.TrackingUrl
	resp.StartDate = deal.StartDate
	resp.EndDate = deal.EndDate
	resp.Status = deal.Status
	resp.CreatedAt = deal.CreatedAt
	resp.UpdatedAt = deal.UpdatedAt

	// 转换商家信息
	if brand != nil {
		resp.Brand = dto.Entity2DtoBrandDetailResp(brand, nil)
	} else {
		resp.Brand = &dto.BrandDetailResp{}
	}

	// 转换分类信息
	if category != nil {
		resp.Category = &categoryDto.CategoryDetailResp{
			ID:   category.ID,
			Slug: category.Slug,
			Name: category.Name,
			Icon: category.Icon,
		}
	} else {
		resp.Category = &categoryDto.CategoryDetailResp{}
	}

	return resp
}

func Entity2DtoDealListResp(pageSize int, page int, total int64, dealList []*entity.Deal, brandMap map[uint64]*brandEntity.Brand, categoryMap map[uint64]*categoryEntity.Category) (resp *DealListResp) {
	resp = &DealListResp{}
	resp.Total = total
	resp.PageSize = pageSize
	resp.Page = page
	for i := range dealList {
		var brand *brandEntity.Brand
		var category *categoryEntity.Category

		if b, ok := brandMap[uint64(dealList[i].BrandID)]; ok {
			brand = b
		} else {
			brand = &brandEntity.Brand{}
		}

		if c, ok := categoryMap[uint64(dealList[i].CategoryID)]; ok {
			category = c
		} else {
			category = nil
		}

		resp.DealList = append(resp.DealList, Entity2DtoDealDetailResp(dealList[i], brand, category))
	}
	return resp
}
