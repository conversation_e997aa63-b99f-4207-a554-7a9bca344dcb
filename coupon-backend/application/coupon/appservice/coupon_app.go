package appservice

import (
	"context"
	"coupon-backend/application/coupon/dto"
	brandEntity "coupon-backend/domain/brand/entity"
	brandService "coupon-backend/domain/brand/service"
	categoryEntity "coupon-backend/domain/category/entity"
	"coupon-backend/domain/coupon/service"
	"coupon-backend/infra/cache"
	"coupon-backend/infra/constant"
	"coupon-backend/infra/ecode"
	"time"

	"github.com/gin-gonic/gin"
)

type CouponAppService interface {
	GetCouponDetailById(ctx *gin.Context, id uint64) (*dto.CouponDetailResp, *ecode.Error)
	GetCouponList(ctx *gin.Context, req *dto.GetCouponListReq) (*dto.CouponListResp, *ecode.Error)
	GetCouponCount(ctx *gin.Context) (int64, *ecode.Error)
}

type CouponAppServiceImpl struct {
	couponService   service.CouponService
	brandService    brandService.BrandService
	categoryService interface {
		GetCategoryDetailById(ctx *gin.Context, id uint64) (*categoryEntity.Category, *ecode.Error)
		GetCategoryListByIDs(ctx *gin.Context, ids []uint64) ([]*categoryEntity.Category, *ecode.Error)
	}
	cacheService cache.CacheService
	keyGen       *cache.CacheKeyGenerator
}

func NewCouponAppService(couponService service.CouponService, brandService brandService.BrandService, categoryService interface {
	GetCategoryDetailById(ctx *gin.Context, id uint64) (*categoryEntity.Category, *ecode.Error)
	GetCategoryListByIDs(ctx *gin.Context, ids []uint64) ([]*categoryEntity.Category, *ecode.Error)
}, cacheService cache.CacheService) CouponAppService {
	return &CouponAppServiceImpl{
		couponService:   couponService,
		brandService:    brandService,
		categoryService: categoryService,
		cacheService:    cacheService,
		keyGen:          cache.NewCacheKeyGenerator(),
	}
}

// GetCouponDetailById 根据ID获取优惠券详情
func (app *CouponAppServiceImpl) GetCouponDetailById(ctx *gin.Context, id uint64) (*dto.CouponDetailResp, *ecode.Error) {
	if id == 0 {
		return nil, ecode.ErrInvalidID
	}

	coupon, err := app.couponService.GetCouponDetailById(ctx, id)
	if err != nil {
		return nil, err
	}

	// 获取商家信息
	var brand *brandEntity.Brand
	var brandCategory *categoryEntity.Category
	if coupon.BrandID > 0 {
		brand, _ = app.brandService.GetBrandDetailById(ctx, uint64(coupon.BrandID))
		// 获取商家的分类信息
		if brand != nil && brand.CategoryID > 0 {
			brandCategory, _ = app.categoryService.GetCategoryDetailById(ctx, uint64(brand.CategoryID))
		}
	}

	// 获取优惠券分类信息
	var category *categoryEntity.Category
	if coupon.CategoryID > 0 {
		category, _ = app.categoryService.GetCategoryDetailById(ctx, uint64(coupon.CategoryID))
	}

	resp := dto.Entity2DtoCouponDetailRespWithBrandCategory(coupon, brand, brandCategory, category)
	return resp, nil
}

// GetCouponList 获取优惠券列表
func (app *CouponAppServiceImpl) GetCouponList(ctx *gin.Context, req *dto.GetCouponListReq) (*dto.CouponListResp, *ecode.Error) {
	// 参数校验
	if req.Page <= 0 {
		req.Page = constant.DefaultPage
	}
	if req.PageSize <= 0 {
		req.PageSize = constant.DefaultPageSize
	}
	if req.PageSize > constant.MaxPageSize {
		req.PageSize = constant.MaxPageSize
	}

	condition := req.Dto2ConditionGetCouponList()
	// 添加状态条件来过滤掉已下线优惠券，用户通过API访问的应该只看到上线的优惠券
	if req.Status == 0 {
		condition["status"] = constant.CouponStatusEnabled
	}

	// 尝试从缓存获取
	cacheKey := app.keyGen.CouponListKey(condition)
	var cachedResp dto.CouponListResp
	if err := app.cacheService.Get(context.Background(), cacheKey, &cachedResp); err == nil {
		return &cachedResp, nil
	}

	coupons, total, err := app.couponService.GetCouponListByCondition(ctx, condition)
	if err != nil {
		return nil, err
	}

	// 简化的批量获取关联数据 - 让domain service处理缓存逻辑
	brandIDs := make([]uint64, 0, len(coupons))
	couponCategoryIDs := make([]uint64, 0, len(coupons))
	for _, coupon := range coupons {
		if coupon.BrandID > 0 {
			brandIDs = append(brandIDs, uint64(coupon.BrandID))
		}
		if coupon.CategoryID > 0 {
			couponCategoryIDs = append(couponCategoryIDs, uint64(coupon.CategoryID))
		}
	}

	// 使用domain service获取品牌信息（service内部处理缓存）
	brands, err := app.brandService.GetBrandListByIDs(ctx, brandIDs)
	if err != nil {
		return nil, err
	}

	brandMap := make(map[uint64]*brandEntity.Brand)
	brandCategoryIDs := make([]uint64, 0)
	for _, brand := range brands {
		brandMap[uint64(brand.ID)] = brand
		if brand.CategoryID > 0 {
			brandCategoryIDs = append(brandCategoryIDs, uint64(brand.CategoryID))
		}
	}

	// 合并所有分类ID
	allCategoryIDs := append(couponCategoryIDs, brandCategoryIDs...)

	// 使用domain service获取分类信息（service内部处理缓存）
	categories, err := app.categoryService.GetCategoryListByIDs(ctx, allCategoryIDs)
	if err != nil {
		return nil, err
	}

	categoryMap := make(map[uint64]*categoryEntity.Category)
	for _, category := range categories {
		categoryMap[uint64(category.ID)] = category
	}

	resp := dto.Entity2DtoCouponListRespWithBrandCategories(req.PageSize, req.Page, total, coupons, brandMap, categoryMap)

	// 异步缓存结果（短时间缓存，避免数据库压力）
	go func() {
		app.cacheService.Set(context.Background(), cacheKey, resp, 2*time.Minute)
	}()

	return resp, nil
}

// GetCouponCount 获取优惠券总数
func (app *CouponAppServiceImpl) GetCouponCount(ctx *gin.Context) (int64, *ecode.Error) {
	return app.couponService.GetCouponCount(ctx)
}
