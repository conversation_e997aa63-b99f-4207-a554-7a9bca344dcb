package dto

import (
	"coupon-backend/application/brand/dto"
	categoryDto "coupon-backend/application/category/dto"
	brandEntity "coupon-backend/domain/brand/entity"
	categoryEntity "coupon-backend/domain/category/entity"
	"coupon-backend/domain/coupon/entity"
	"time"
)

type GetCouponListReq struct {
	Page         int    `json:"page" form:"page"`
	PageSize     int    `json:"page_size" form:"page_size"`
	Search       string `json:"search" form:"search"`
	Sort         string `json:"sort" form:"sort"`
	BrandID      uint64 `json:"brand_id" form:"brand_id"`
	CategoryID   uint64 `json:"category_id" form:"category_id"`
	CategorySlug string `json:"category_slug" form:"category_slug"`
	IsFeatured   bool   `json:"is_featured" form:"is_featured"`
	IsExclusive  bool   `json:"is_exclusive" form:"is_exclusive"`
	Status       int    `json:"status" form:"status"`
}

// 响应对象

type CouponDetailResp struct {
	ID          uint                            `json:"id"`
	CreatedAt   time.Time                       `json:"created_at"`
	UpdatedAt   time.Time                       `json:"updated_at"`
	BrandID     uint                            `json:"brand_id"`
	Brand       *dto.BrandDetailResp            `json:"brand"` // 商家信息
	CategoryID  uint                            `json:"category_id"`
	Category    *categoryDto.CategoryDetailResp `json:"category"` // 分类信息
	Code        string                          `json:"code"`
	IsFeatured  bool                            `json:"is_featured"`
	IsExclusive bool                            `json:"is_exclusive"`
	Discount    string                          `json:"discount"`
	Name        string                          `json:"name"`
	Description string                          `json:"description"`
	StartDate   *time.Time                      `json:"start_date"`
	EndDate     *time.Time                      `json:"end_date"`
	Status      int                             `json:"status"`
}

type CouponListResp struct {
	Total      int64               `json:"total"`
	Page       int                 `json:"page"`
	PageSize   int                 `json:"page_size"`
	CouponList []*CouponDetailResp `json:"coupon_list"`
}

// Reset 重置对象状态，用于对象池
func (r *CouponListResp) Reset() {
	r.Total = 0
	r.Page = 0
	r.PageSize = 0
	r.CouponList = r.CouponList[:0] // 保留容量，只重置长度
}

// Dto2ConditionGetCouponList 获取优惠券列表请求转换为 condition
func (req *GetCouponListReq) Dto2ConditionGetCouponList() (condition map[string]interface{}) {
	condition = map[string]interface{}{}
	// 分页处理
	offset := (req.Page - 1) * req.PageSize
	condition["offset"] = offset
	if req.Page > 0 {
		condition["page"] = req.Page
	}
	if req.PageSize > 0 {
		condition["page_size"] = req.PageSize
	}
	if req.Search != "" {
		condition["search"] = req.Search
	}
	if req.Sort != "" {
		condition["sort"] = req.Sort
	}
	if req.BrandID > 0 {
		condition["brand_id"] = req.BrandID
	}
	if req.CategoryID > 0 {
		condition["category_id"] = req.CategoryID
	}
	if req.CategorySlug != "" {
		condition["category_slug"] = req.CategorySlug
	}
	if req.IsFeatured {
		condition["is_featured"] = req.IsFeatured
	}
	if req.IsExclusive {
		condition["is_exclusive"] = req.IsExclusive
	}
	if req.Status > 0 {
		condition["status"] = req.Status
	}
	return condition
}

func Entity2DtoCouponDetailResp(coupon *entity.Coupon, brand *brandEntity.Brand, category *categoryEntity.Category) (resp *CouponDetailResp) {
	return Entity2DtoCouponDetailRespWithBrandCategory(coupon, brand, nil, category)
}

func Entity2DtoCouponDetailRespWithBrandCategory(coupon *entity.Coupon, brand *brandEntity.Brand, brandCategory *categoryEntity.Category, category *categoryEntity.Category) (resp *CouponDetailResp) {
	resp = &CouponDetailResp{}
	resp.ID = coupon.ID
	resp.BrandID = coupon.BrandID
	resp.CategoryID = coupon.CategoryID
	resp.Code = coupon.Code
	resp.IsFeatured = coupon.IsFeatured
	resp.IsExclusive = coupon.IsExclusive
	resp.Discount = coupon.Discount
	resp.Name = coupon.Name
	resp.Description = coupon.Description
	resp.StartDate = coupon.StartDate
	resp.EndDate = coupon.EndDate
	resp.Status = coupon.Status
	resp.CreatedAt = coupon.CreatedAt
	resp.UpdatedAt = coupon.UpdatedAt

	// 转换商家信息
	if brand != nil {
		resp.Brand = dto.Entity2DtoBrandDetailResp(brand, brandCategory)
	} else {
		resp.Brand = &dto.BrandDetailResp{}
	}

	// 转换分类信息
	if category != nil {
		resp.Category = &categoryDto.CategoryDetailResp{
			ID:   category.ID,
			Slug: category.Slug,
			Name: category.Name,
			Icon: category.Icon,
		}
	} else {
		resp.Category = &categoryDto.CategoryDetailResp{}
	}

	return resp
}

func Entity2DtoCouponListResp(pageSize int, page int, total int64, couponList []*entity.Coupon, brandMap map[uint64]*brandEntity.Brand, categoryMap map[uint64]*categoryEntity.Category) (resp *CouponListResp) {
	return Entity2DtoCouponListRespWithBrandCategories(pageSize, page, total, couponList, brandMap, categoryMap)
}

func Entity2DtoCouponListRespWithBrandCategories(pageSize int, page int, total int64, couponList []*entity.Coupon, brandMap map[uint64]*brandEntity.Brand, categoryMap map[uint64]*categoryEntity.Category) (resp *CouponListResp) {
	resp = &CouponListResp{}
	resp.Total = total
	resp.PageSize = pageSize
	resp.Page = page
	for i := range couponList {
		var brand *brandEntity.Brand
		var brandCategory *categoryEntity.Category
		var category *categoryEntity.Category

		if b, ok := brandMap[uint64(couponList[i].BrandID)]; ok {
			brand = b
			// 获取商家的分类信息
			if brand.CategoryID > 0 {
				if bc, ok := categoryMap[uint64(brand.CategoryID)]; ok {
					brandCategory = bc
				}
			}
		} else {
			brand = &brandEntity.Brand{}
		}

		if c, ok := categoryMap[uint64(couponList[i].CategoryID)]; ok {
			category = c
		} else {
			category = nil
		}

		resp.CouponList = append(resp.CouponList, Entity2DtoCouponDetailRespWithBrandCategory(couponList[i], brand, brandCategory, category))
	}
	return resp
}
