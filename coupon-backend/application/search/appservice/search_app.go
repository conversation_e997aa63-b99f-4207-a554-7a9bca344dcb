package appservice

import (
	brandAppService "coupon-backend/application/brand/appservice"
	brandDto "coupon-backend/application/brand/dto"
	couponAppService "coupon-backend/application/coupon/appservice"
	couponDto "coupon-backend/application/coupon/dto"
	dealAppService "coupon-backend/application/deal/appservice"
	dealDto "coupon-backend/application/deal/dto"
	"coupon-backend/application/search/dto"
	"coupon-backend/infra/ecode"

	"github.com/gin-gonic/gin"
)

type SearchAppService interface {
	Search(ctx *gin.Context, req *dto.SearchRequest) (*dto.SearchResponse, *ecode.Error)
}

type SearchAppServiceImpl struct {
	brandAppService  brandAppService.BrandAppService
	couponAppService couponAppService.CouponAppService
	dealAppService   dealAppService.DealAppService
}

func NewSearchAppService(
	brandAppService brandAppService.BrandAppService,
	couponAppService couponAppService.CouponAppService,
	dealAppService dealAppService.DealAppService,
) SearchAppService {
	return &SearchAppServiceImpl{
		brandAppService:  brandAppService,
		couponAppService: couponAppService,
		dealAppService:   dealAppService,
	}
}

// Search 统一搜索
func (app *SearchAppServiceImpl) Search(ctx *gin.Context, req *dto.SearchRequest) (*dto.SearchResponse, *ecode.Error) {
	// 验证请求参数
	if err := req.ValidateSearchRequest(); err != nil {
		return nil, ecode.ErrInvalidParameter
	}

	searchType := req.GetSearchType()

	// 获取统计信息
	stats, err := app.getSearchStats(ctx)
	if err != nil {
		return nil, err
	}

	response := &dto.SearchResponse{
		Query:    req.Query,
		Type:     searchType,
		Page:     req.Page,
		PageSize: req.PageSize,
		Stats:    stats,
	}

	var totalCount int64

	// 根据搜索类型执行搜索
	switch searchType {
	case "brand":
		brands, brandCount, err := app.searchBrands(ctx, req)
		if err != nil {
			return nil, err
		}
		response.Brands = brands
		totalCount = brandCount

	case "coupon":
		coupons, couponCount, err := app.searchCoupons(ctx, req)
		if err != nil {
			return nil, err
		}
		response.Coupons = coupons
		totalCount = couponCount

	case "deal":
		deals, dealCount, err := app.searchDeals(ctx, req)
		if err != nil {
			return nil, err
		}
		response.Deals = deals
		totalCount = dealCount

	default: // "all"
		// 搜索所有类型
		pageSize := req.PageSize

		// 搜索商家
		brandReq := &brandDto.GetBrandListReq{
			Page:     1,
			PageSize: pageSize,
			Search:   req.Query,
			Sort:     req.Sort,
		}
		brands, err := app.brandAppService.GetBrandList(ctx, brandReq)
		if err != nil {
			return nil, err
		}
		response.Brands = brands

		// 搜索优惠券
		couponReq := &couponDto.GetCouponListReq{
			Page:     1,
			PageSize: pageSize,
			Search:   req.Query,
			Sort:     req.Sort,
		}
		coupons, err := app.couponAppService.GetCouponList(ctx, couponReq)
		if err != nil {
			return nil, err
		}
		response.Coupons = coupons

		// 搜索优惠活动
		dealReq := &dealDto.GetDealListReq{
			Page:     1,
			PageSize: pageSize,
			Search:   req.Query,
			Sort:     req.Sort,
		}
		deals, err := app.dealAppService.GetDealList(ctx, dealReq)
		if err != nil {
			return nil, err
		}
		response.Deals = deals

		totalCount = brands.Total + coupons.Total + deals.Total
	}

	response.Total = totalCount
	return response, nil
}

// searchBrands 搜索商家
func (app *SearchAppServiceImpl) searchBrands(ctx *gin.Context, req *dto.SearchRequest) (*brandDto.BrandListResp, int64, *ecode.Error) {
	brandReq := &brandDto.GetBrandListReq{
		Page:     req.Page,
		PageSize: req.PageSize,
		Search:   req.Query,
		Sort:     req.Sort,
	}
	brands, err := app.brandAppService.GetBrandList(ctx, brandReq)
	if err != nil {
		return nil, 0, err
	}
	return brands, brands.Total, nil
}

// searchCoupons 搜索优惠券
func (app *SearchAppServiceImpl) searchCoupons(ctx *gin.Context, req *dto.SearchRequest) (*couponDto.CouponListResp, int64, *ecode.Error) {
	couponReq := &couponDto.GetCouponListReq{
		Page:     req.Page,
		PageSize: req.PageSize,
		Search:   req.Query,
		Sort:     req.Sort,
	}
	coupons, err := app.couponAppService.GetCouponList(ctx, couponReq)
	if err != nil {
		return nil, 0, err
	}
	return coupons, coupons.Total, nil
}

// searchDeals 搜索优惠活动
func (app *SearchAppServiceImpl) searchDeals(ctx *gin.Context, req *dto.SearchRequest) (*dealDto.DealListResp, int64, *ecode.Error) {
	dealReq := &dealDto.GetDealListReq{
		Page:     req.Page,
		PageSize: req.PageSize,
		Search:   req.Query,
		Sort:     req.Sort,
	}
	deals, err := app.dealAppService.GetDealList(ctx, dealReq)
	if err != nil {
		return nil, 0, err
	}
	return deals, deals.Total, nil
}

// getSearchStats 获取搜索统计（私有方法）
func (app *SearchAppServiceImpl) getSearchStats(ctx *gin.Context) (*dto.SearchStats, *ecode.Error) {
	// 获取各类型总数
	brandCount, err := app.brandAppService.GetBrandCount(ctx)
	if err != nil {
		return nil, err
	}

	couponCount, err := app.couponAppService.GetCouponCount(ctx)
	if err != nil {
		return nil, err
	}

	dealCount, err := app.dealAppService.GetDealCount(ctx)
	if err != nil {
		return nil, err
	}

	return &dto.SearchStats{
		TotalBrands:  brandCount,
		TotalCoupons: couponCount,
		TotalDeals:   dealCount,
	}, nil
}
