package dto

import (
	brandDto "coupon-backend/application/brand/dto"
	couponDto "coupon-backend/application/coupon/dto"
	dealDto "coupon-backend/application/deal/dto"
)

// SearchRequest 统一搜索请求
type SearchRequest struct {
	Query    string `json:"query" form:"query" binding:"required"` // 搜索关键词
	Page     int    `json:"page" form:"page"`                      // 页码
	PageSize int    `json:"page_size" form:"page_size"`            // 每页数量
	Type     string `json:"type" form:"type"`                      // 搜索类型：all, brand, coupon, deal
	Sort     string `json:"sort" form:"sort"`                      // 排序方式
}

// SearchResponse 统一搜索响应
type SearchResponse struct {
	Query    string                    `json:"query"`             // 搜索关键词
	Type     string                    `json:"type"`              // 搜索类型
	Total    int64                     `json:"total"`             // 当前搜索结果总数
	Page     int                       `json:"page"`              // 当前页
	PageSize int                       `json:"page_size"`         // 每页数量
	Brands   *brandDto.BrandListResp   `json:"brands,omitempty"`  // 商家搜索结果
	Coupons  *couponDto.CouponListResp `json:"coupons,omitempty"` // 优惠券搜索结果
	Deals    *dealDto.DealListResp     `json:"deals,omitempty"`   // 优惠活动搜索结果
	Stats    *SearchStats              `json:"stats"`             // 搜索统计信息
}

// SearchStats 搜索统计
type SearchStats struct {
	TotalBrands  int64 `json:"total_brands"`  // 总商家数
	TotalCoupons int64 `json:"total_coupons"` // 总优惠券数
	TotalDeals   int64 `json:"total_deals"`   // 总优惠活动数
}

// ValidateSearchRequest 验证搜索请求
func (req *SearchRequest) ValidateSearchRequest() error {
	if req.Query == "" {
		return nil // 允许空查询，返回所有结果
	}
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}
	if req.PageSize > 100 {
		req.PageSize = 100
	}
	if req.Type == "" {
		req.Type = "all"
	}
	return nil
}

// GetSearchType 获取搜索类型
func (req *SearchRequest) GetSearchType() string {
	switch req.Type {
	case "brand", "coupon", "deal":
		return req.Type
	default:
		return "all"
	}
}
