# 🛡️ 优雅退出功能文档

## 功能概览

为API服务和Task服务实现了完整的优雅退出功能，确保在收到终止信号时能够正确清理所有资源，避免数据丢失和资源泄漏。

## 🚀 API服务优雅退出

### 支持的信号
- `SIGINT` (Ctrl+C)
- `SIGTERM` (终止信号)
- `SIGHUP` (挂起信号)

### 退出流程
```
1. 接收信号 → 2. 停止HTTP服务器 → 3. 清理容器资源 → 4. 完成退出
   (立即)      (等待30秒)        (清理所有资源)    (安全退出)
```

### 实现细节
```go
// 优雅关闭流程
func gracefulShutdown(server *http.Server, container *container.Container) {
    // 1. 创建30秒超时上下文
    ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
    defer cancel()

    // 2. 停止HTTP服务器，等待现有请求完成
    if err := server.Shutdown(ctx); err != nil {
        server.Close() // 超时则强制关闭
    }

    // 3. 清理容器资源
    container.Cleanup()
}
```

### 清理的资源
- ✅ HTTP连接和请求
- ✅ 数据库连接池
- ✅ Redis连接
- ✅ 缓存清理goroutine
- ✅ 异步日志服务
- ✅ 自适应连接池

## 📋 Task服务优雅退出

### 支持的信号
- `SIGINT` (Ctrl+C)
- `SIGTERM` (终止信号)
- `SIGHUP` (挂起信号)

### 退出流程
```
1. 接收信号 → 2. 停止调度器 → 3. 等待任务完成 → 4. 关闭连接 → 5. 完成退出
   (立即)      (停止新任务)    (等待5秒)       (清理资源)    (安全退出)
```

### 实现细节
```go
// 优雅关闭流程
func gracefulShutdown(taskScheduler *scheduler.Scheduler) {
    // 1. 停止调度器（不再启动新任务）
    taskScheduler.Stop()

    // 2. 等待当前运行的任务完成
    time.Sleep(5 * time.Second)

    // 3. 关闭数据库连接
    persistence.CloseDatabase()

    // 4. 关闭Redis连接
    persistence.CloseRedis()
}
```

### 清理的资源
- ✅ gocron调度器
- ✅ 运行中的任务
- ✅ 数据库连接
- ✅ Redis连接
- ✅ 缓存服务

## 🔧 容器资源清理

### 清理顺序
```
1. 缓存清理服务 → 2. 连接池 → 3. 异步日志 → 4. 数据库 → 5. Redis
   (停止goroutine)  (关闭池)   (刷新日志)    (关闭连接)  (关闭连接)
```

### 详细实现
```go
func (c *Container) Cleanup() {
    // 1. 停止缓存清理服务
    if brandService, ok := c.BrandService.(*brandService.BrandServiceImpl); ok {
        brandService.StopCacheCleanup()
    }
    if categoryService, ok := c.CategoryService.(*categoryService.CategoryServiceImpl); ok {
        categoryService.StopCacheCleanup()
    }

    // 2. 关闭自适应连接池
    if adaptivePool := database.GetGlobalAdaptivePool(); adaptivePool != nil {
        adaptivePool.Close()
    }

    // 3. 关闭异步日志
    if asyncLogger := logger.GetGlobalAsyncLogger(); asyncLogger != nil {
        asyncLogger.Close()
    }

    // 4. 关闭数据库连接
    persistence.CloseDatabase()

    // 5. 关闭Redis连接
    persistence.CloseRedis()
}
```

## 📊 安全保障

### 超时机制
| 服务 | 超时时间 | 超时处理 |
|------|----------|----------|
| API HTTP服务器 | 30秒 | 强制关闭 |
| Task任务等待 | 5秒 | 继续清理 |
| 数据库关闭 | 无超时 | 记录错误 |
| Redis关闭 | 无超时 | 记录错误 |

### 错误处理
```go
// 数据库关闭错误处理
if err := persistence.CloseDatabase(); err != nil {
    log.Printf("⚠️ Error closing database: %v", err)
} else {
    log.Println("✅ Database connections closed")
}

// Redis关闭错误处理
if err := persistence.CloseRedis(); err != nil {
    log.Printf("⚠️ Error closing Redis: %v", err)
} else {
    log.Println("✅ Redis connections closed")
}
```

### 日志记录
- 🚀 启动日志：服务启动成功
- 🛑 信号日志：收到退出信号
- 📋 流程日志：退出流程开始
- 🔄 步骤日志：每个清理步骤
- ✅ 完成日志：退出流程完成
- ⚠️ 错误日志：清理过程中的错误

## 🧪 测试方法

### API服务测试
```bash
# 启动API服务
go run cmd/api/main.go

# 发送SIGTERM信号
kill -TERM <pid>

# 或使用Ctrl+C
# 观察日志输出确认优雅退出
```

### Task服务测试
```bash
# 启动Task服务
go run cmd/task/main.go

# 发送SIGTERM信号
kill -TERM <pid>

# 或使用Ctrl+C
# 观察日志输出确认优雅退出
```

### 预期日志输出
```
🛑 Received signal: terminated, shutting down API server...
📋 Starting graceful shutdown process...
🔄 Stopping HTTP server...
✅ HTTP server stopped gracefully
🧹 Cleaning up container resources...
🔄 Stopping cache cleanup services...
✅ Cache cleanup services stopped
🔗 Closing adaptive connection pool...
✅ Adaptive connection pool closed
📝 Closing async logger...
✅ Async logger closed
🗄️ Closing database connections...
✅ Database connections closed
🔴 Closing Redis connections...
✅ Redis connections closed
✅ Container cleanup completed
✅ API Server shutdown completed
```

## 🔒 生产环境部署

### Docker容器
```dockerfile
# 确保容器能正确处理信号
STOPSIGNAL SIGTERM

# 设置合理的停止超时
# docker run --stop-timeout=60 your-app
```

### Kubernetes部署
```yaml
spec:
  terminationGracePeriodSeconds: 60  # 给足够时间优雅退出
  containers:
  - name: api
    lifecycle:
      preStop:
        exec:
          command: ["/bin/sh", "-c", "sleep 5"]  # 给负载均衡器时间移除实例
```

### 系统服务
```ini
[Unit]
Description=Coupon API Service
After=network.target

[Service]
Type=simple
ExecStart=/path/to/api
KillSignal=SIGTERM
TimeoutStopSec=60
Restart=always

[Install]
WantedBy=multi-user.target
```

## 🎯 最佳实践

### 1. 信号处理
- 监听多种信号（SIGINT, SIGTERM, SIGHUP）
- 使用缓冲通道避免信号丢失
- 记录收到的具体信号类型

### 2. 超时控制
- 设置合理的超时时间
- 超时后强制关闭避免无限等待
- 不同资源使用不同的超时策略

### 3. 资源清理顺序
- 先停止接受新请求/任务
- 等待当前请求/任务完成
- 按依赖关系逆序清理资源
- 最后清理基础设施资源

### 4. 错误处理
- 清理过程中的错误不应阻止后续清理
- 记录所有错误但继续执行
- 提供详细的日志信息

### 5. 监控和告警
- 监控优雅退出的成功率
- 设置退出时间过长的告警
- 记录资源清理的详细指标

这套优雅退出机制确保了服务在任何情况下都能安全、干净地关闭，避免数据丢失和资源泄漏。
