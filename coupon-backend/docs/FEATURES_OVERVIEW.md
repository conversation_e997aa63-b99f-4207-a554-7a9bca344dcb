# 🎯 后端功能总览

## 系统架构

### 分层架构设计
```
┌─────────────────────────────────────────┐
│              Interfaces                 │  ← API层、路由、中间件
├─────────────────────────────────────────┤
│             Application                 │  ← 应用服务、业务编排
├─────────────────────────────────────────┤
│               Domain                    │  ← 领域服务、业务逻辑
├─────────────────────────────────────────┤
│            Infrastructure               │  ← 数据库、缓存、配置
└─────────────────────────────────────────┘
```

### 技术栈
- **框架**: Gin (Go Web框架)
- **数据库**: PostgreSQL + GORM
- **缓存**: Redis
- **配置**: YAML + 环境变量
- **日志**: 结构化日志
- **文档**: Swagger

## 核心功能模块

### 1. 分类管理 (Category)
#### 功能特性
- ✅ 分类CRUD操作
- ✅ 分类列表查询（分页、搜索、排序）
- ✅ 按Slug查询分类详情
- ✅ 批量操作支持

#### API端点
```
GET    /api/v1/categories           # 获取分类列表
GET    /api/v1/categories/{id}      # 根据ID获取分类详情
GET    /api/v1/categories/slug/{slug} # 根据Slug获取分类详情
```

#### 数据模型
```go
type Category struct {
    ID        uint      `json:"id"`
    Slug      string    `json:"slug"`      // 唯一标识
    Name      string    `json:"name"`      // 分类名称
    Icon      string    `json:"icon"`      // 图标
    CreatedAt time.Time `json:"created_at"`
    UpdatedAt time.Time `json:"updated_at"`
}
```

### 2. 商家管理 (Brand)
#### 功能特性
- ✅ 商家CRUD操作
- ✅ 商家列表查询（分页、搜索、排序）
- ✅ 按Slug查询商家详情
- ✅ 商家分类关联
- ✅ 推荐商家筛选
- ✅ 批量操作支持

#### API端点
```
GET    /api/v1/brands              # 获取商家列表
GET    /api/v1/brands/{id}         # 根据ID获取商家详情
GET    /api/v1/brands/slug/{slug}  # 根据Slug获取商家详情
```

#### 数据模型
```go
type Brand struct {
    ID          uint      `json:"id"`
    Slug        string    `json:"slug"`        // 唯一标识
    Name        string    `json:"name"`        // 商家名称
    Description string    `json:"description"` // 描述
    Logo        string    `json:"logo"`        // Logo URL
    Website     string    `json:"website"`     // 官网
    CategoryID  uint      `json:"category_id"` // 分类ID
    Featured    bool      `json:"featured"`    // 是否推荐
    Status      int       `json:"status"`      // 状态
    CreatedAt   time.Time `json:"created_at"`
    UpdatedAt   time.Time `json:"updated_at"`
}
```

### 3. 优惠券管理 (Coupon)
#### 功能特性
- ✅ 优惠券CRUD操作
- ✅ 优惠券列表查询（分页、搜索、排序）
- ✅ 多维度筛选（商家、分类、推荐、独家）
- ✅ 商家和分类关联
- ✅ 平台数据同步支持
- ✅ 批量操作支持

#### API端点
```
GET    /api/v1/coupons             # 获取优惠券列表
GET    /api/v1/coupons/{id}        # 根据ID获取优惠券详情
```

#### 查询参数
```
page, page_size    # 分页参数
search            # 搜索关键词
sort              # 排序方式
brand_id          # 商家ID筛选
category_id       # 分类ID筛选
category_slug     # 分类Slug筛选
is_featured       # 推荐筛选
is_exclusive      # 独家筛选
status            # 状态筛选
```

#### 数据模型
```go
type Coupon struct {
    ID               uint       `json:"id"`
    BrandID          uint       `json:"brand_id"`
    CategoryID       uint       `json:"category_id"`
    Code             string     `json:"code"`
    Name             string     `json:"name"`
    Description      string     `json:"description"`
    Discount         string     `json:"discount"`
    IsFeatured       bool       `json:"is_featured"`
    IsExclusive      bool       `json:"is_exclusive"`
    StartDate        *time.Time `json:"start_date"`
    EndDate          *time.Time `json:"end_date"`
    Status           int        `json:"status"`
    PlatformType     string     `json:"platform_type"`
    PlatformCouponID string     `json:"platform_coupon_id"`
    CreatedAt        time.Time  `json:"created_at"`
    UpdatedAt        time.Time  `json:"updated_at"`
}
```

### 4. 优惠活动管理 (Deal)
#### 功能特性
- ✅ 优惠活动CRUD操作
- ✅ 活动列表查询（分页、搜索、排序）
- ✅ 多维度筛选（商家、分类、热门、推荐）
- ✅ 商家和分类关联
- ✅ 平台数据同步支持
- ✅ 批量操作支持

#### API端点
```
GET    /api/v1/deals               # 获取优惠活动列表
GET    /api/v1/deals/{id}          # 根据ID获取活动详情
```

#### 数据模型
```go
type Deal struct {
    ID             uint       `json:"id"`
    BrandID        uint       `json:"brand_id"`
    CategoryID     uint       `json:"category_id"`
    Code           string     `json:"code"`
    Title          string     `json:"title"`
    Description    string     `json:"description"`
    Img            string     `json:"img"`
    Discount       string     `json:"discount"`
    IsHotDeal      bool       `json:"is_hot_deal"`
    IsFeatured     bool       `json:"is_featured"`
    OriginURL      string     `json:"origin_url"`
    TrackingUrl    string     `json:"tracking_url"`
    StartDate      *time.Time `json:"start_date"`
    EndDate        *time.Time `json:"end_date"`
    Status         int        `json:"status"`
    PlatformType   string     `json:"platform_type"`
    PlatformDealID string     `json:"platform_deal_id"`
    CreatedAt      time.Time  `json:"created_at"`
    UpdatedAt      time.Time  `json:"updated_at"`
}
```

### 5. 统一搜索 (Search)
#### 功能特性
- ✅ 跨实体搜索（商家、优惠券、活动）
- ✅ 按类型筛选搜索结果
- ✅ 统一的搜索接口
- ✅ 分页和排序支持

#### API端点
```
GET    /api/v1/search              # 统一搜索接口
```

#### 查询参数
```
q             # 搜索关键词
type          # 搜索类型 (brand/coupon/deal/all)
page          # 页码
page_size     # 每页数量
```

## 配置管理

### 多环境配置
- ✅ 环境隔离（local/live）
- ✅ 服务特定配置（api.yaml/task.yaml）
- ✅ 动态配置加载
- ✅ 配置热更新支持

### 配置结构
```
configs/
├── local/
│   ├── api.yaml     # API服务配置
│   └── task.yaml    # 定时任务配置
└── live/
    ├── api.yaml     # 生产API配置
    └── task.yaml    # 生产任务配置
```

## 定时任务系统

### 任务类型
- ✅ 品牌数据同步
- ✅ 优惠券数据同步
- ✅ 优惠活动数据同步
- ✅ 可配置的Cron表达式
- ✅ 任务超时控制

### 配置示例
```yaml
task:
  brand_sync_cron: "0 2 * * *"      # 每天凌晨2点
  coupon_sync_cron: "0 */4 * * *"   # 每4小时
  deal_sync_cron: "0 */6 * * *"     # 每6小时
  task_timeout: 1800s               # 30分钟超时
```

## 数据关联

### 实体关系
```
Category (1) ←→ (N) Brand (1) ←→ (N) Coupon
Category (1) ←→ (N) Deal
Brand (1) ←→ (N) Deal
```

### 关联查询
- ✅ 优惠券包含商家和分类信息
- ✅ 活动包含商家和分类信息
- ✅ 商家包含分类信息
- ✅ 支持按关联字段筛选

## 响应格式

### 统一响应结构
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total": 100,
    "page": 1,
    "page_size": 20,
    "list": [...]
  }
}
```

### 分页信息
- ✅ 总数统计
- ✅ 页码信息
- ✅ 每页数量
- ✅ 数据列表

## 错误处理

### 统一错误码
- ✅ HTTP状态码映射
- ✅ 业务错误码
- ✅ 错误信息国际化
- ✅ 详细错误描述

### 常见错误
```
400 - 请求参数错误
404 - 资源不存在
500 - 服务器内部错误
```
