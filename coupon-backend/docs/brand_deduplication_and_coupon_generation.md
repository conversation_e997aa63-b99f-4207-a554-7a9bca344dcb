# 品牌去重和优惠券生成功能

## 概述

本文档描述了新实现的品牌去重逻辑和自动优惠券生成功能。

## 品牌去重功能

### 功能描述

在品牌同步定时任务中，系统会自动检测并处理重复的品牌数据。去重规则如下：

1. **相同site_url的处理**：如果多个品牌有相同的site_url，系统认为它们是同一个商家
2. **同平台去重**：如果同一平台（如linkbux）有多个相同site_url的品牌，只保留第一个，其他标记为下线（status=0）
3. **跨平台优先级**：如果不同平台有相同site_url的品牌，使用优先级高的平台数据，其他平台的数据标记为下线

### 平台优先级配置

在`constant.go`中的`MerchantsAccountList`配置中添加了`priority`字段：

```go
var MerchantsAccountList = []map[string]interface{}{
    {
        "account_name": "linkbux01",
        "type":         AccountTypeLinkbux,
        "token":        "mj2A8CXcblQZozBC",
        "limit":        1000,
        "priority":     1, // 数字越小优先级越高
    },
    // 未来可以添加其他平台
    {
        "account_name": "awin01",
        "type":         "awin",
        "token":        "xxx",
        "limit":        1000,
        "priority":     2, // 优先级低于linkbux
    },
}
```

### 数据库变更

添加了新字段到brands表：

```sql
ALTER TABLE brands ADD COLUMN platform_priority INTEGER DEFAULT 999;
```

### API行为变更

- **Application层**：通过API获取的品牌数据总是添加status=1过滤条件，只返回激活的商家
- **Domain层和Repository层**：不包含任何status过滤逻辑，可以获取所有商家数据（包括status=0）
- **特殊参数逻辑**：所有业务逻辑过滤都放在Application层，Domain层和Repository层保持纯净

## 自动优惠券生成功能

### 功能描述

在优惠券同步定时任务完成后，系统会自动为没有优惠券的品牌生成随机优惠券，避免网站上出现大量0优惠券的品牌。

### 生成规则

1. **检测逻辑**：使用品牌的`total_coupons`字段判断是否需要生成优惠券（值为0表示没有优惠券）
2. **数量**：每个品牌随机生成1-5个优惠券
3. **优惠券代码**：使用真实的优惠券代码模式（如SAVE20、GET15等）
4. **折扣类型**：
   - 百分比折扣：5%-50%
   - 金额折扣：$5-$100
5. **特色标记**：20%概率为特色优惠券
6. **独家标记**：30%概率为独家优惠券
7. **有效期**：开始日期为最近7天内，结束日期为7-90天后
8. **计数更新**：生成完成后立即更新品牌的`total_coupons`字段

### 生成的优惠券特点

- **真实性**：优惠券代码、名称、描述都使用真实的模式，不会让用户看出是随机生成的
- **多样性**：使用多种优惠券代码模式和描述模板
- **合理性**：折扣金额和百分比都在合理范围内
- **标识**：platform_type设置为"generated"以便识别

### 示例生成的优惠券

```
代码: SAVE25
名称: Welcome Discount  
描述: Save on your favorite items with this exclusive discount.
折扣: 25% Off
有效期: 2025-07-30 到 2025-10-28
```

## 实现细节

### 核心方法

1. **deduplicateBrandsBySiteURL()**: 执行品牌去重逻辑
2. **updateAllBrandCounts()**: 更新所有品牌的优惠券和优惠活动数量
3. **updateAllBrandCouponCounts()**: 更新所有品牌的优惠券数量
4. **updateAllBrandDealCounts()**: 更新所有品牌的优惠活动数量
5. **generateRandomCouponsForBrandsWithoutCoupons()**: 为没有优惠券的品牌生成随机优惠券
6. **generateRandomCouponsForBrand()**: 为单个品牌生成优惠券
7. **generateRealisticCouponCode()**: 生成真实的优惠券代码
8. **generateRealisticDiscount()**: 生成真实的折扣描述

### 执行时机

- **品牌去重**：在品牌同步任务完成后自动执行
- **品牌计数更新**：在每次品牌、优惠券、优惠活动同步完成后自动执行
- **优惠券生成**：在优惠券同步任务完成后自动执行
- **计数同步**：确保每次同步任务都会更新相关的计数字段，保持数据一致性

### 日志记录

系统会详细记录去重和生成过程：

```
🔄 Starting brand deduplication by site_url...
Found 3 brands with same site_url: testbrand.com
Platform linkbux has 2 duplicates for site_url testbrand.com, keeping 1, deactivating 1
Multiple platforms for site_url testbrand.com, keeping platform linkbux (priority 1), deactivating 1 others
✅ Brand deduplication completed. Deactivated 2 duplicate brands

🎯 Starting random coupon generation for brands without coupons...
Found 1250 brands without coupons out of 18098 total brands
✅ Generated 3750 random coupons for 1250 brands
```

## 测试

可以使用测试文件验证功能：

```bash
cd coupon-backend
go run cmd/test/test_brand_deduplication.go
```

## 数据库迁移

运行迁移脚本添加新字段：

```bash
psql -d your_database -f migrations/add_platform_priority_to_brands.sql
```

## 部署步骤

1. **运行数据库迁移**：
   ```bash
   psql -d your_database -f migrations/add_platform_priority_to_brands.sql
   ```

2. **重新编译和部署**：
   ```bash
   # 编译API服务
   go build -o api cmd/api/main.go

   # 编译任务服务
   go build -o task cmd/task/main.go

   # 部署到服务器
   ```

3. **验证功能**：
   - 检查品牌同步日志，确认去重逻辑正常工作
   - 检查优惠券同步日志，确认随机优惠券生成正常
   - 通过API验证只返回激活的品牌（status=1）

## 监控和维护

1. **日志监控**：关注以下日志信息
   - 品牌去重统计：`Brand deduplication completed. Deactivated X duplicate brands`
   - 优惠券生成统计：`Generated X random coupons for Y brands`

2. **数据库监控**：
   - 监控brands表的status字段分布
   - 监控生成的优惠券数量（platform_type='generated'）

3. **性能监控**：
   - 去重操作的执行时间
   - 优惠券生成的执行时间

## 注意事项

1. **数据安全**：去重操作只是标记品牌为下线（status=0），不会真正删除数据
2. **性能优化**：添加了必要的数据库索引来优化查询性能
3. **可扩展性**：优先级配置支持未来添加更多平台
4. **真实性**：生成的优惠券看起来像真实的优惠券，提升用户体验
5. **向后兼容**：现有API行为保持不变，只返回激活的品牌
6. **测试验证**：提供了完整的测试文件来验证功能正确性
