package repository

import (
	"coupon-backend/domain/category/entity"
	"coupon-backend/infra/ecode"
	"github.com/gin-gonic/gin"
)

// CategoryRepository 分类仓储接口
type CategoryRepository interface {
	GetCategoryDetailById(ctx *gin.Context, id uint64) (*entity.Category, *ecode.Error)
	GetCategoryDetailBySlug(ctx *gin.Context, slug string) (*entity.Category, *ecode.Error)
	GetCategoryListByCondition(ctx *gin.Context, condition map[string]interface{}) ([]*entity.Category, int64, *ecode.Error)
	GetCategoryCount(ctx *gin.Context) (int64, *ecode.Error)
	GetCategoryListByIDs(ctx *gin.Context, ids []uint64) ([]*entity.Category, *ecode.Error)
	CreateCategory(ctx *gin.Context, category *entity.Category) *ecode.Error
	UpdateCategory(ctx *gin.Context, category *entity.Category) *ecode.Error
	DeleteCategory(ctx *gin.Context, id uint64) *ecode.Error
	BatchCreateCategories(ctx *gin.Context, categories []*entity.Category) *ecode.Error
	BatchUpdateCategories(ctx *gin.Context, categories []*entity.Category) *ecode.Error
}
