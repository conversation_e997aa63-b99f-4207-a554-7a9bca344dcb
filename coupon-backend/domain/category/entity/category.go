package entity

import "time"

// Category represents a category entity (single level only)
type Category struct {
	ID        uint      `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	Slug      string    `json:"slug" gorm:"size:120;not null;uniqueIndex"`
	Name      string    `json:"name" gorm:"size:100;not null"`
	Icon      string    `json:"icon" gorm:"size:100"`
}
