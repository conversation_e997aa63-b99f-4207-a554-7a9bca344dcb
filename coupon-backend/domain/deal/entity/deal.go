package entity

import "time"

// Deal represents a deal entity
type Deal struct {
	ID          uint      `json:"id" gorm:"primaryKey"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	BrandID     uint      `json:"brand_id" gorm:"not null"`
	CategoryID  uint      `json:"category_id" gorm:"not null"`
	Code        string    `json:"code" gorm:"type:text"`
	Title       string    `json:"title" gorm:"type:text"`
	Description string    `json:"description" gorm:"type:text"`
	Img         string    `json:"img" gorm:"type:text"`
	IsHotDeal   bool      `json:"is_hot_deal" gorm:"default:false"`
	IsFeatured  bool      `json:"is_featured" gorm:"default:false"`
	Discount    string    `json:"discount" gorm:"type:text"`

	OriginURL   string `json:"origin_url" gorm:"type:text"`
	TrackingUrl string `json:"tracking_url" gorm:"type:text"`

	StartDate *time.Time `json:"start_date"`
	EndDate   *time.Time `json:"end_date"`
	Status    int        `json:"status" gorm:"default:1;not null"`

	PlatformType   string `gorm:"type:varchar(63);not null" json:"platform_type"` // 平台类型：cj、awin
	PlatformDealID string `json:"platform_deal_id" gorm:"size:255"`
}
