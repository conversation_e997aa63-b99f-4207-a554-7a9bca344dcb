package repository

import (
	"coupon-backend/domain/deal/entity"
	"coupon-backend/infra/ecode"

	"github.com/gin-gonic/gin"
)

// DealRepository 优惠活动仓储接口
type DealRepository interface {
	GetDealDetailById(ctx *gin.Context, id uint64) (*entity.Deal, *ecode.Error)
	GetDealListByCondition(ctx *gin.Context, condition map[string]interface{}) ([]*entity.Deal, int64, *ecode.Error)
	GetDealCount(ctx *gin.Context) (int64, *ecode.Error)
	GetDealListByIDs(ctx *gin.Context, ids []uint64) ([]*entity.Deal, *ecode.Error)
	GetDealListByBrandIDs(ctx *gin.Context, brandIDs []uint64) ([]*entity.Deal, *ecode.Error)
	CreateDeal(ctx *gin.Context, deal *entity.Deal) *ecode.Error
	UpdateDeal(ctx *gin.Context, deal *entity.Deal) *ecode.Error
	DeleteDeal(ctx *gin.Context, id uint64) *ecode.Error
	UpdateDealStatus(ctx *gin.Context, id uint64, status int8) *ecode.Error
	BatchCreateDeals(ctx *gin.Context, deals []*entity.Deal) *ecode.Error
	BatchUpdateDeals(ctx *gin.Context, deals []*entity.Deal) *ecode.Error

	// 同步相关方法
	GetDealsByPlatform(ctx *gin.Context, platformType string) ([]*entity.Deal, *ecode.Error)
	FindDealByPlatformAndDealID(ctx *gin.Context, platformType, dealID string) (*entity.Deal, *ecode.Error)
	FindDealsByPlatformAndDealIDs(ctx *gin.Context, platformType string, dealIDs []string) ([]*entity.Deal, *ecode.Error)
}
