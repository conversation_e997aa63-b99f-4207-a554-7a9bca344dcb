package service

import (
	"coupon-backend/domain/deal/entity"
	"coupon-backend/domain/deal/repository"
	"coupon-backend/infra/ecode"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
)

type DealService interface {
	GetDealDetailById(ctx *gin.Context, id uint64) (*entity.Deal, *ecode.Error)
	GetDealListByCondition(ctx *gin.Context, condition map[string]interface{}) ([]*entity.Deal, int64, *ecode.Error)
	GetDealListByIDs(ctx *gin.Context, ids []uint64) ([]*entity.Deal, *ecode.Error)
	GetDealListByBrandIDs(ctx *gin.Context, brandIDs []uint64) ([]*entity.Deal, *ecode.Error)
	CreateDeal(ctx *gin.Context, deal *entity.Deal) *ecode.Error
	UpdateDeal(ctx *gin.Context, deal *entity.Deal) *ecode.Error
	DeleteDeal(ctx *gin.Context, id uint64) *ecode.Error
	GetDealCount(ctx *gin.Context) (int64, *ecode.Error)
	BatchCreateDeals(ctx *gin.Context, deals []*entity.Deal) *ecode.Error
	BatchUpdateDeals(ctx *gin.Context, deals []*entity.Deal) *ecode.Error
}

type DealServiceImpl struct {
	repo repository.DealRepository
	// 本地缓存
	localCache     map[string]interface{}
	localCacheTTL  map[string]time.Time
	localCacheLock sync.RWMutex
}

func NewDealService(repo repository.DealRepository) DealService {
	service := &DealServiceImpl{
		repo:           repo,
		localCache:     make(map[string]interface{}),
		localCacheTTL:  make(map[string]time.Time),
		localCacheLock: sync.RWMutex{},
	}
	return service
}

// GetDealDetailById 根据ID获取优惠活动详情
func (s *DealServiceImpl) GetDealDetailById(ctx *gin.Context, id uint64) (*entity.Deal, *ecode.Error) {
	return s.repo.GetDealDetailById(ctx, id)
}

// GetDealListByCondition 根据条件获取优惠活动列表
func (s *DealServiceImpl) GetDealListByCondition(ctx *gin.Context, condition map[string]interface{}) ([]*entity.Deal, int64, *ecode.Error) {
	return s.repo.GetDealListByCondition(ctx, condition)
}

// GetDealListByIDs 根据ID列表获取优惠活动列表
func (s *DealServiceImpl) GetDealListByIDs(ctx *gin.Context, ids []uint64) ([]*entity.Deal, *ecode.Error) {
	return s.repo.GetDealListByIDs(ctx, ids)
}

// GetDealListByBrandIDs 根据商家ID列表获取优惠活动列表
func (s *DealServiceImpl) GetDealListByBrandIDs(ctx *gin.Context, brandIDs []uint64) ([]*entity.Deal, *ecode.Error) {
	return s.repo.GetDealListByBrandIDs(ctx, brandIDs)
}

// CreateDeal 创建优惠活动
func (s *DealServiceImpl) CreateDeal(ctx *gin.Context, deal *entity.Deal) *ecode.Error {
	return s.repo.CreateDeal(ctx, deal)
}

// UpdateDeal 更新优惠活动
func (s *DealServiceImpl) UpdateDeal(ctx *gin.Context, deal *entity.Deal) *ecode.Error {
	return s.repo.UpdateDeal(ctx, deal)
}

// DeleteDeal 删除优惠活动
func (s *DealServiceImpl) DeleteDeal(ctx *gin.Context, id uint64) *ecode.Error {
	return s.repo.DeleteDeal(ctx, id)
}

// GetDealCount 获取优惠活动总数
func (s *DealServiceImpl) GetDealCount(ctx *gin.Context) (int64, *ecode.Error) {
	return s.repo.GetDealCount(ctx)
}

// BatchCreateDeals 批量创建优惠活动
func (s *DealServiceImpl) BatchCreateDeals(ctx *gin.Context, deals []*entity.Deal) *ecode.Error {
	return s.repo.BatchCreateDeals(ctx, deals)
}

// BatchUpdateDeals 批量更新优惠活动
func (s *DealServiceImpl) BatchUpdateDeals(ctx *gin.Context, deals []*entity.Deal) *ecode.Error {
	return s.repo.BatchUpdateDeals(ctx, deals)
}
