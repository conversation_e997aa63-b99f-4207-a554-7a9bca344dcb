package service

import (
	"coupon-backend/domain/article/entity"
	"coupon-backend/domain/article/repository"
	"coupon-backend/infra/ecode"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
)

type ArticleService interface {
	GetArticleDetailById(ctx *gin.Context, id uint64) (*entity.Article, *ecode.Error)
	GetArticleDetailBySlug(ctx *gin.Context, slug string) (*entity.Article, *ecode.Error)
	GetArticleListByCondition(ctx *gin.Context, condition map[string]interface{}) ([]*entity.Article, int64, *ecode.Error)
	GetArticleListByIDs(ctx *gin.Context, ids []uint64) ([]*entity.Article, *ecode.Error)
	CreateArticle(ctx *gin.Context, article *entity.Article) *ecode.Error
	UpdateArticle(ctx *gin.Context, article *entity.Article) *ecode.Error
	DeleteArticle(ctx *gin.Context, id uint64) *ecode.Error
	GetArticleCount(ctx *gin.Context) (int64, *ecode.Error)
}

type ArticleServiceImpl struct {
	repo repository.ArticleRepository
	// 本地缓存
	localCache     map[string]interface{}
	localCacheTTL  map[string]time.Time
	localCacheLock sync.RWMutex
}

func NewArticleService(repo repository.ArticleRepository) ArticleService {
	service := &ArticleServiceImpl{
		repo:           repo,
		localCache:     make(map[string]interface{}),
		localCacheTTL:  make(map[string]time.Time),
		localCacheLock: sync.RWMutex{},
	}
	return service
}

// GetArticleDetailById 根据ID获取文章详情
func (s *ArticleServiceImpl) GetArticleDetailById(ctx *gin.Context, id uint64) (*entity.Article, *ecode.Error) {
	return s.repo.GetArticleDetailById(ctx, id)
}

// GetArticleDetailBySlug 根据Slug获取文章详情
func (s *ArticleServiceImpl) GetArticleDetailBySlug(ctx *gin.Context, slug string) (*entity.Article, *ecode.Error) {
	return s.repo.GetArticleDetailBySlug(ctx, slug)
}

// GetArticleListByCondition 根据条件获取文章列表
func (s *ArticleServiceImpl) GetArticleListByCondition(ctx *gin.Context, condition map[string]interface{}) ([]*entity.Article, int64, *ecode.Error) {
	return s.repo.GetArticleListByCondition(ctx, condition)
}

// GetArticleListByIDs 根据ID列表获取文章列表
func (s *ArticleServiceImpl) GetArticleListByIDs(ctx *gin.Context, ids []uint64) ([]*entity.Article, *ecode.Error) {
	return s.repo.GetArticleListByIDs(ctx, ids)
}

// CreateArticle 创建文章
func (s *ArticleServiceImpl) CreateArticle(ctx *gin.Context, article *entity.Article) *ecode.Error {
	return s.repo.CreateArticle(ctx, article)
}

// UpdateArticle 更新文章
func (s *ArticleServiceImpl) UpdateArticle(ctx *gin.Context, article *entity.Article) *ecode.Error {
	return s.repo.UpdateArticle(ctx, article)
}

// DeleteArticle 删除文章
func (s *ArticleServiceImpl) DeleteArticle(ctx *gin.Context, id uint64) *ecode.Error {
	return s.repo.DeleteArticle(ctx, id)
}

// GetArticleCount 获取文章总数
func (s *ArticleServiceImpl) GetArticleCount(ctx *gin.Context) (int64, *ecode.Error) {
	return s.repo.GetArticleCount(ctx)
}
