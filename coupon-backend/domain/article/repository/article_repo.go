package repository

import (
	"coupon-backend/domain/article/entity"
	"coupon-backend/infra/ecode"

	"github.com/gin-gonic/gin"
)

// ArticleRepository 文章仓储接口
type ArticleRepository interface {
	GetArticleDetailById(ctx *gin.Context, id uint64) (*entity.Article, *ecode.Error)
	GetArticleDetailBySlug(ctx *gin.Context, slug string) (*entity.Article, *ecode.Error)
	GetArticleListByCondition(ctx *gin.Context, condition map[string]interface{}) ([]*entity.Article, int64, *ecode.Error)
	GetArticleCount(ctx *gin.Context) (int64, *ecode.Error)
	GetArticleListByIDs(ctx *gin.Context, ids []uint64) ([]*entity.Article, *ecode.Error)
	CreateArticle(ctx *gin.Context, article *entity.Article) *ecode.Error
	UpdateArticle(ctx *gin.Context, article *entity.Article) *ecode.Error
	DeleteArticle(ctx *gin.Context, id uint64) *ecode.Error
	UpdateArticleStatus(ctx *gin.Context, id uint64, published bool) *ecode.Error
}
