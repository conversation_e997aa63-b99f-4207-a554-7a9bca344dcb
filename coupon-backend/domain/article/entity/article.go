package entity

import (
	"time"
)

// Article represents an article entity with optimized field ordering for memory alignment
type Article struct {
	// 8-byte aligned fields first
	Id          uint64    `json:"id" gorm:"primaryKey"`
	BrandId     uint64    `json:"brand_id"`
	CategoryId  uint64    `json:"category_id"`
	PublishedAt time.Time `json:"published_at" gorm:"index:idx_article_published"`
	CreatedAt   time.Time `json:"created_at" gorm:"index:idx_article_created"`
	UpdatedAt   time.Time `json:"updated_at"`

	// String fields (pointer size - 8 bytes on 64-bit)
	Slug          string `json:"slug" gorm:"uniqueIndex:idx_article_slug;type:varchar(300);not null" validate:"required,min=3,max=300,slug"`
	Title         string `json:"title" gorm:"type:varchar(300);not null;index:idx_article_title" validate:"required,min=3,max=300"`
	Description   string `json:"description" gorm:"type:text" validate:"max=500"`
	Content       string `json:"content" gorm:"type:text;not null" validate:"required,min=100"`
	FeaturedImage string `json:"featured_image" gorm:"type:varchar(500)" validate:"omitempty,url,max=500"`
	MetaTitle     string `json:"meta_title" gorm:"type:varchar(70)" validate:"omitempty,max=70"`
	MetaDesc      string `json:"meta_description" gorm:"type:varchar(160)" validate:"omitempty,max=160"`
	AuthorName    string `json:"author_name" gorm:"type:varchar(100)" validate:"omitempty,max=100"`

	// Boolean fields (1 byte each, grouped together for better packing)
	Featured  bool `json:"featured" gorm:"type:boolean;default:false;index:idx_article_featured"`
	Published bool `json:"published" gorm:"type:boolean;default:false;index:idx_article_published_status"`
}

// TableName returns the table name for Article entity
func (Article) TableName() string {
	return "articles"
}
