package service

import (
	"coupon-backend/domain/coupon/entity"
	"coupon-backend/domain/coupon/repository"
	"coupon-backend/infra/ecode"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
)

type CouponService interface {
	GetCouponDetailById(ctx *gin.Context, id uint64) (*entity.Coupon, *ecode.Error)
	GetCouponListByCondition(ctx *gin.Context, condition map[string]interface{}) ([]*entity.Coupon, int64, *ecode.Error)
	GetCouponListByIDs(ctx *gin.Context, ids []uint64) ([]*entity.Coupon, *ecode.Error)
	GetCouponListByBrandIDs(ctx *gin.Context, brandIDs []uint64) ([]*entity.Coupon, *ecode.Error)
	CreateCoupon(ctx *gin.Context, coupon *entity.Coupon) *ecode.Error
	UpdateCoupon(ctx *gin.Context, coupon *entity.Coupon) *ecode.Error
	DeleteCoupon(ctx *gin.Context, id uint64) *ecode.Error
	GetCouponCount(ctx *gin.Context) (int64, *ecode.Error)
	BatchCreateCoupons(ctx *gin.Context, coupons []*entity.Coupon) *ecode.Error
	BatchUpdateCoupons(ctx *gin.Context, coupons []*entity.Coupon) *ecode.Error
}

type CouponServiceImpl struct {
	repo repository.CouponRepository
	// 本地缓存
	localCache     map[string]interface{}
	localCacheTTL  map[string]time.Time
	localCacheLock sync.RWMutex
}

func NewCouponService(repo repository.CouponRepository) CouponService {
	service := &CouponServiceImpl{
		repo:           repo,
		localCache:     make(map[string]interface{}),
		localCacheTTL:  make(map[string]time.Time),
		localCacheLock: sync.RWMutex{},
	}
	return service
}

// GetCouponDetailById 根据ID获取优惠券详情
func (s *CouponServiceImpl) GetCouponDetailById(ctx *gin.Context, id uint64) (*entity.Coupon, *ecode.Error) {
	return s.repo.GetCouponDetailById(ctx, id)
}

// GetCouponListByCondition 根据条件获取优惠券列表
func (s *CouponServiceImpl) GetCouponListByCondition(ctx *gin.Context, condition map[string]interface{}) ([]*entity.Coupon, int64, *ecode.Error) {
	return s.repo.GetCouponListByCondition(ctx, condition)
}

// GetCouponListByIDs 根据ID列表获取优惠券列表
func (s *CouponServiceImpl) GetCouponListByIDs(ctx *gin.Context, ids []uint64) ([]*entity.Coupon, *ecode.Error) {
	return s.repo.GetCouponListByIDs(ctx, ids)
}

// GetCouponListByBrandIDs 根据商家ID列表获取优惠券列表
func (s *CouponServiceImpl) GetCouponListByBrandIDs(ctx *gin.Context, brandIDs []uint64) ([]*entity.Coupon, *ecode.Error) {
	return s.repo.GetCouponListByBrandIDs(ctx, brandIDs)
}

// CreateCoupon 创建优惠券
func (s *CouponServiceImpl) CreateCoupon(ctx *gin.Context, coupon *entity.Coupon) *ecode.Error {
	return s.repo.CreateCoupon(ctx, coupon)
}

// UpdateCoupon 更新优惠券
func (s *CouponServiceImpl) UpdateCoupon(ctx *gin.Context, coupon *entity.Coupon) *ecode.Error {
	return s.repo.UpdateCoupon(ctx, coupon)
}

// DeleteCoupon 删除优惠券
func (s *CouponServiceImpl) DeleteCoupon(ctx *gin.Context, id uint64) *ecode.Error {
	return s.repo.DeleteCoupon(ctx, id)
}

// GetCouponCount 获取优惠券总数
func (s *CouponServiceImpl) GetCouponCount(ctx *gin.Context) (int64, *ecode.Error) {
	return s.repo.GetCouponCount(ctx)
}

// BatchCreateCoupons 批量创建优惠券
func (s *CouponServiceImpl) BatchCreateCoupons(ctx *gin.Context, coupons []*entity.Coupon) *ecode.Error {
	return s.repo.BatchCreateCoupons(ctx, coupons)
}

// BatchUpdateCoupons 批量更新优惠券
func (s *CouponServiceImpl) BatchUpdateCoupons(ctx *gin.Context, coupons []*entity.Coupon) *ecode.Error {
	return s.repo.BatchUpdateCoupons(ctx, coupons)
}
