package entity

import (
	brandEntity "coupon-backend/domain/brand/entity"
	categoryEntity "coupon-backend/domain/category/entity"
	"time"
)

type Coupon struct {
	ID          uint       `json:"id" gorm:"primaryKey"`
	CreatedAt   time.Time  `json:"created_at"`
	UpdatedAt   time.Time  `json:"updated_at"`
	BrandID     uint       `json:"brand_id" gorm:"not null"`
	CategoryID  uint       `json:"category_id" gorm:"not null"`
	Code        string     `json:"code" gorm:"type:text"`
	IsFeatured  bool       `json:"is_featured" gorm:"default:false"`
	IsExclusive bool       `json:"is_exclusive" gorm:"default:false"`
	Discount    string     `json:"discount" gorm:"type:text"`
	Name        string     `json:"name" gorm:"type:text"`
	Description string     `json:"description" gorm:"type:text"`
	StartDate   *time.Time `json:"start_date"`
	EndDate     *time.Time `json:"end_date"`
	Status      int        `json:"status" gorm:"default:1;not null"`

	PlatformType     string `gorm:"type:varchar(63);not null" json:"platform_type"` // 平台类型：cj、awin
	PlatformCouponID string `json:"platform_coupon_id" gorm:"size:255"`

	// 关联关系
	Brand    *brandEntity.Brand       `json:"brand,omitempty" gorm:"foreignKey:BrandID"`
	Category *categoryEntity.Category `json:"category,omitempty" gorm:"foreignKey:CategoryID"`
}
