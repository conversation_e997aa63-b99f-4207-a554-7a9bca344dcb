package repository

import (
	"coupon-backend/domain/coupon/entity"
	"coupon-backend/infra/ecode"

	"github.com/gin-gonic/gin"
)

// CouponRepository 优惠券仓储接口
type CouponRepository interface {
	GetCouponDetailById(ctx *gin.Context, id uint64) (*entity.Coupon, *ecode.Error)
	GetCouponListByCondition(ctx *gin.Context, condition map[string]interface{}) ([]*entity.Coupon, int64, *ecode.Error)
	GetCouponCount(ctx *gin.Context) (int64, *ecode.Error)
	GetCouponListByIDs(ctx *gin.Context, ids []uint64) ([]*entity.Coupon, *ecode.Error)
	GetCouponListByBrandIDs(ctx *gin.Context, brandIDs []uint64) ([]*entity.Coupon, *ecode.Error)
	CreateCoupon(ctx *gin.Context, coupon *entity.Coupon) *ecode.Error
	UpdateCoupon(ctx *gin.Context, coupon *entity.Coupon) *ecode.Error
	DeleteCoupon(ctx *gin.Context, id uint64) *ecode.Error
	UpdateCouponStatus(ctx *gin.Context, id uint64, status int8) *ecode.Error
	BatchCreateCoupons(ctx *gin.Context, coupons []*entity.Coupon) *ecode.Error
	BatchUpdateCoupons(ctx *gin.Context, coupons []*entity.Coupon) *ecode.Error

	// 同步相关方法
	GetCouponsByPlatform(ctx *gin.Context, platformType string) ([]*entity.Coupon, *ecode.Error)
	FindCouponByPlatformAndCouponID(ctx *gin.Context, platformType, couponID string) (*entity.Coupon, *ecode.Error)
	FindCouponsByPlatformAndCouponIDs(ctx *gin.Context, platformType string, couponIDs []string) ([]*entity.Coupon, *ecode.Error)
}
