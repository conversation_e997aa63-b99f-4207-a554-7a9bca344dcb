package service

import (
	brandEntity "coupon-backend/domain/brand/entity"
	brandRepo "coupon-backend/domain/brand/repository"
	couponEntity "coupon-backend/domain/coupon/entity"
	couponRepo "coupon-backend/domain/coupon/repository"
	dealEntity "coupon-backend/domain/deal/entity"
	dealRepo "coupon-backend/domain/deal/repository"
	"coupon-backend/domain/task/entity"
	"coupon-backend/infra/cache"
	"coupon-backend/infra/constant"
	"coupon-backend/infra/ecode"
	"coupon-backend/infra/external_gateway/fatcouponlib"
	"coupon-backend/infra/external_gateway/linkbuxlib"
	"coupon-backend/infra/persistence"
	"coupon-backend/infra/utils"
	"coupon-backend/infra/warmup"
	"fmt"
	"log"
	"math/rand"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

// TaskService task service interface
type TaskService interface {
	// Sync tasks
	SyncBrands() error
	SyncCoupons() error
	SyncDeals() error

	// Multi-platform sync methods (internal implementation)
	syncAllPlatformsBrands() error
	syncAllPlatformsCoupons() error
	syncAllPlatformsDeals() error

	// Cache warmup task
	WarmupCache() error

	// Task management
	CreateTask(taskType entity.TaskType) (*entity.Task, error)
	UpdateTaskStatus(taskID uint, status entity.TaskStatus, message string) error
	CompleteTask(taskID uint, processedCount, successCount, failedCount int) error
	FailTask(taskID uint, errorMsg string) error

	// Task logging
	LogInfo(taskID uint, message string, data any) error
	LogWarn(taskID uint, message string, data any) error
	LogError(taskID uint, message string, data any) error
}

type TaskServiceImpl struct {
	// Cache service dependencies
	cacheService  cache.CacheService
	warmupService *warmup.WarmupService

	// Repository dependencies for sync
	brandRepo  brandRepo.BrandRepository
	couponRepo couponRepo.CouponRepository
	dealRepo   dealRepo.DealRepository
}

func NewTaskService(cacheService cache.CacheService, brandRepository brandRepo.BrandRepository, couponRepository couponRepo.CouponRepository, dealRepository dealRepo.DealRepository) TaskService {
	// 🚀 初始化域名匹配器（单例模式，只会加载一次）
	domainMatcher := utils.GetDomainMatcher()
	stats := domainMatcher.GetStats()
	log.Printf("🎯 Domain matcher initialized: %+v", stats)

	return &TaskServiceImpl{
		cacheService:  cacheService,
		warmupService: warmup.NewWarmupService(cacheService),
		brandRepo:     brandRepository,
		couponRepo:    couponRepository,
		dealRepo:      dealRepository,
	}
}

// SyncBrands syncs brand data
func (s *TaskServiceImpl) SyncBrands() error {
	task, err := s.CreateTask(entity.TaskTypeSyncBrand)
	if err != nil {
		return err
	}

	// Start executing task
	s.UpdateTaskStatus(task.ID, entity.TaskStatusRunning, "Starting brand data sync")
	s.LogInfo(task.ID, "Starting brand data sync", nil)

	// Actual sync logic
	processedCount := 0
	successCount := 0
	failedCount := 0

	// Sync from all platforms
	s.LogInfo(task.ID, "Syncing brand data from all platforms", nil)
	if err := s.syncAllPlatformsBrands(); err != nil {
		s.LogError(task.ID, "Failed to sync brands from all platforms", map[string]any{"error": err.Error()})
		failedCount = 1
	} else {
		successCount = 1
	}
	processedCount = 1

	// Complete task
	s.CompleteTask(task.ID, processedCount, successCount, failedCount)
	s.LogInfo(task.ID, "Brand data sync completed", map[string]any{
		"processed": processedCount,
		"success":   successCount,
		"failed":    failedCount,
	})

	log.Printf("Brand sync task %d completed", task.ID)
	return nil
}

// SyncCoupons syncs coupon data
func (s *TaskServiceImpl) SyncCoupons() error {
	task, err := s.CreateTask(entity.TaskTypeSyncCoupon)
	if err != nil {
		return err
	}

	// Start executing task
	s.UpdateTaskStatus(task.ID, entity.TaskStatusRunning, "Starting coupon data sync")
	s.LogInfo(task.ID, "Starting coupon data sync", nil)

	// Actual sync logic
	processedCount := 0
	successCount := 0
	failedCount := 0

	// Sync from all platforms
	s.LogInfo(task.ID, "Syncing coupon data from all platforms", nil)
	if err := s.syncAllPlatformsCoupons(); err != nil {
		s.LogError(task.ID, "Failed to sync coupons from all platforms", map[string]any{"error": err.Error()})
		failedCount = 1
	} else {
		successCount = 1
	}

	// Generate random coupons for brands without coupons
	s.LogInfo(task.ID, "Generating random coupons for brands without coupons", nil)
	if err := s.generateRandomCouponsForBrandsWithoutCoupons(); err != nil {
		s.LogError(task.ID, "Failed to generate random coupons", map[string]any{"error": err.Error()})
		// Don't fail the entire task for this
	}
	processedCount = 1

	// Complete task
	s.CompleteTask(task.ID, processedCount, successCount, failedCount)
	s.LogInfo(task.ID, "Coupon data sync completed", map[string]any{
		"processed": processedCount,
		"success":   successCount,
		"failed":    failedCount,
	})

	log.Printf("Coupon sync task %d completed", task.ID)
	return nil
}

// SyncDeals syncs deal data
func (s *TaskServiceImpl) SyncDeals() error {
	task, err := s.CreateTask(entity.TaskTypeSyncDeal)
	if err != nil {
		return err
	}

	// Start executing task
	s.UpdateTaskStatus(task.ID, entity.TaskStatusRunning, "Starting deal data sync")
	s.LogInfo(task.ID, "Starting deal data sync", nil)

	// Actual sync logic
	processedCount := 0
	successCount := 0
	failedCount := 0

	// Sync from all platforms
	s.LogInfo(task.ID, "Syncing deal data from all platforms", nil)
	if err := s.syncAllPlatformsDeals(); err != nil {
		s.LogError(task.ID, "Failed to sync deals from all platforms", map[string]any{"error": err.Error()})
		failedCount = 1
	} else {
		successCount = 1
	}
	processedCount = 1

	// Complete task
	s.CompleteTask(task.ID, processedCount, successCount, failedCount)
	s.LogInfo(task.ID, "Deal data sync completed", map[string]any{
		"processed": processedCount,
		"success":   successCount,
		"failed":    failedCount,
	})

	log.Printf("Deal sync task %d completed", task.ID)
	return nil
}

// CreateTask creates a task
func (s *TaskServiceImpl) CreateTask(taskType entity.TaskType) (*entity.Task, error) {
	now := time.Now()
	task := &entity.Task{
		ID:        uint(now.Unix()), // Use timestamp as ID (production should use database auto-increment ID)
		Type:      taskType,
		Status:    entity.TaskStatusPending,
		CreatedAt: now,
		UpdatedAt: now,
	}

	// In actual production environment, this should be saved to database
	// Currently as in-memory storage, will be lost after restart
	log.Printf("Created task %d of type %s", task.ID, taskType)
	return task, nil
}

// UpdateTaskStatus updates task status
func (s *TaskServiceImpl) UpdateTaskStatus(taskID uint, status entity.TaskStatus, message string) error {
	// In actual production environment, this should update task status in database
	log.Printf("Task %d status updated to %d: %s", taskID, status, message)
	return nil
}

// CompleteTask completes a task
func (s *TaskServiceImpl) CompleteTask(taskID uint, processedCount, successCount, failedCount int) error {
	// In actual production environment, this should update task completion info in database
	log.Printf("Task %d completed: processed=%d, success=%d, failed=%d",
		taskID, processedCount, successCount, failedCount)
	return nil
}

// FailTask marks a task as failed
func (s *TaskServiceImpl) FailTask(taskID uint, errorMsg string) error {
	// In actual production environment, this should update task failure info in database
	log.Printf("Task %d failed: %s", taskID, errorMsg)
	return nil
}

// LogInfo logs info message
func (s *TaskServiceImpl) LogInfo(taskID uint, message string, data any) error {
	// In actual production environment, this should be saved to database or logging system
	log.Printf("Task %d INFO: %s", taskID, message)
	return nil
}

// LogWarn logs warning message
func (s *TaskServiceImpl) LogWarn(taskID uint, message string, data any) error {
	// In actual production environment, this should be saved to database or logging system
	log.Printf("Task %d WARN: %s", taskID, message)
	return nil
}

// LogError logs error message
func (s *TaskServiceImpl) LogError(taskID uint, message string, data any) error {
	// In actual production environment, this should be saved to database or logging system
	log.Printf("Task %d ERROR: %s", taskID, message)
	return nil
}

// WarmupCache cache warmup task
func (s *TaskServiceImpl) WarmupCache() error {
	task, err := s.CreateTask(entity.TaskTypeWarmupCache)
	if err != nil {
		return err
	}

	// Start executing task
	s.UpdateTaskStatus(task.ID, entity.TaskStatusRunning, "Starting cache warmup")
	s.LogInfo(task.ID, "Starting cache warmup", nil)

	// Execute warmup
	start := time.Now()
	s.warmupService.StartWarmup()
	duration := time.Since(start)

	// Complete task
	s.LogInfo(task.ID, "Cache warmup completed", map[string]any{
		"duration": duration.String(),
	})
	s.CompleteTask(task.ID, 1, 1, 0)

	log.Printf("Cache warmup task completed, duration: %v", duration)
	return nil
}

// syncAllPlatformsBrands syncs brand data from all configured platforms
func (s *TaskServiceImpl) syncAllPlatformsBrands() error {
	log.Println("🔄 Starting multi-platform brands sync...")

	ctx := &gin.Context{}

	var totalCreated, totalUpdated int

	// Process each account
	for _, accountConfig := range constant.MerchantsAccountList {
		platformType := accountConfig["type"].(string)
		priority := accountConfig["priority"].(int)

		// Get existing brands from database for this platform
		existingBrands, brandErr := s.brandRepo.GetBrandsByPlatform(ctx, platformType)
		if brandErr != nil {
			continue
		}

		// Create map for quick lookup
		existingBrandMap := make(map[string]*brandEntity.Brand)
		for _, brand := range existingBrands {
			if brand.PlatformMerchantID != "" {
				existingBrandMap[brand.PlatformMerchantID] = brand
			}
		}

		var createBrands []*brandEntity.Brand
		var updateBrands []*brandEntity.Brand

		accountName := accountConfig["account_name"].(string)
		// Get data from external API based on platform type
		var externalData []map[string]interface{}
		var apiErr error

		switch platformType {
		case constant.AccountTypeLinkbux:
			externalData, apiErr = linkbuxlib.BatchGetMerchants(accountName, accountConfig)
		case constant.AccountTypeFatcoupon:
			externalData, apiErr = fatcouponlib.BatchGetMerchants(accountName, accountConfig)
		default:
			log.Printf("Unsupported platform type: %s", platformType)
			continue
		}

		if apiErr != nil {
			log.Printf("Failed to get merchants from %s: %v", accountName, apiErr)
			continue
		}

		// Process each merchant
		for _, data := range externalData {
			merchantID := data["id"].(string)

			brand := s.mapToBrand(data, platformType, priority)
			if brand == nil {
				continue
			}

			if existingBrand, exists := existingBrandMap[merchantID]; exists {
				// Update existing brand
				brand.ID = existingBrand.ID
				brand.CreatedAt = existingBrand.CreatedAt
				updateBrands = append(updateBrands, brand)
			} else {
				// Create new brand
				createBrands = append(createBrands, brand)
			}
		}

		// Batch create new brands for this platform
		if len(createBrands) > 0 {
			if err := s.brandRepo.BatchCreateBrands(ctx, createBrands); err != nil {
				log.Printf("Failed to batch create brands for platform %s: %v", platformType, err)
				continue
			}
			log.Printf("✅ Created %d new brands for platform %s", len(createBrands), platformType)
			totalCreated += len(createBrands)
		}

		// Batch update existing brands for this platform
		if len(updateBrands) > 0 {
			if err := s.brandRepo.BatchUpdateBrands(ctx, updateBrands); err != nil {
				log.Printf("Failed to batch update brands for platform %s: %v", platformType, err)
				continue
			}
			log.Printf("✅ Updated %d existing brands for platform %s", len(updateBrands), platformType)
			totalUpdated += len(updateBrands)
		}
	}

	log.Printf("✅ Multi-platform brands sync completed. Total Created: %d, Total Updated: %d", totalCreated, totalUpdated)

	// 执行去重逻辑
	if err := s.deduplicateBrandsBySiteURL(ctx); err != nil {
		log.Printf("Failed to deduplicate brands: %v", err)
	}

	// 更新所有品牌的优惠券和优惠活动数量
	if err := s.updateAllBrandCounts(ctx); err != nil {
		log.Printf("Failed to update brand counts: %v", err)
	}

	return nil
}

// deduplicateBrandsBySiteURL 根据site_url去重品牌
func (s *TaskServiceImpl) deduplicateBrandsBySiteURL(ctx *gin.Context) error {
	log.Println("🔄 Starting brand deduplication by site_url...")

	// 获取所有品牌（包括已下线的）
	allBrands, err := s.brandRepo.GetAllBrandsIncludingInactive(ctx)
	if err != nil {
		return fmt.Errorf("failed to get all brands: %w", err)
	}

	// 按site_url分组
	siteURLGroups := make(map[string][]*brandEntity.Brand)
	for _, brand := range allBrands {
		if brand.SiteURL != "" {
			siteURLGroups[brand.SiteURL] = append(siteURLGroups[brand.SiteURL], brand)
		}
	}

	var deactivatedCount int

	// 处理每个site_url组
	for siteURL, brands := range siteURLGroups {
		if len(brands) <= 1 {
			continue // 没有重复，跳过
		}

		log.Printf("Found %d brands with same site_url: %s", len(brands), siteURL)

		// 按平台类型分组
		platformGroups := make(map[string][]*brandEntity.Brand)
		for _, brand := range brands {
			platformGroups[brand.PlatformType] = append(platformGroups[brand.PlatformType], brand)
		}

		var keepBrands []*brandEntity.Brand
		var deactivateBrands []*brandEntity.Brand

		// 处理每个平台组
		for platformType, platformBrands := range platformGroups {
			if len(platformBrands) > 1 {
				// 同平台有重复，只保留第一个，其他下线
				keepBrands = append(keepBrands, platformBrands[0])
				deactivateBrands = append(deactivateBrands, platformBrands[1:]...)
				log.Printf("Platform %s has %d duplicates for site_url %s, keeping 1, deactivating %d",
					platformType, len(platformBrands), siteURL, len(platformBrands)-1)
			} else {
				keepBrands = append(keepBrands, platformBrands[0])
			}
		}

		// 如果有多个平台的品牌，按优先级选择
		if len(keepBrands) > 1 {
			// 按优先级排序（数字越小优先级越高）
			sort.Slice(keepBrands, func(i, j int) bool {
				return keepBrands[i].PlatformPriority < keepBrands[j].PlatformPriority
			})

			// 只保留优先级最高的，其他下线
			finalKeep := keepBrands[0]
			deactivateBrands = append(deactivateBrands, keepBrands[1:]...)

			log.Printf("Multiple platforms for site_url %s, keeping platform %s (priority %d), deactivating %d others",
				siteURL, finalKeep.PlatformType, finalKeep.PlatformPriority, len(keepBrands)-1)
		}

		// 批量下线重复的品牌
		if len(deactivateBrands) > 0 {
			var brandIDs []uint
			for _, brand := range deactivateBrands {
				if brand.Status == 1 { // 只下线当前激活的
					brandIDs = append(brandIDs, brand.ID)
				}
			}

			if len(brandIDs) > 0 {
				if err := s.brandRepo.BatchUpdateBrandStatus(ctx, brandIDs, 0); err != nil {
					log.Printf("Failed to deactivate duplicate brands for site_url %s: %v", siteURL, err)
					continue
				}
				deactivatedCount += len(brandIDs)
			}
		}
	}

	log.Printf("✅ Brand deduplication completed. Deactivated %d duplicate brands", deactivatedCount)
	return nil
}

// mapToBrand converts external data to Brand entity
func (s *TaskServiceImpl) mapToBrand(data map[string]interface{}, platformType string, priority int) *brandEntity.Brand {
	// Extract required fields
	merchantID, ok := data["id"].(string)
	if !ok || merchantID == "" {
		return nil
	}

	name, ok := data["name"].(string)
	if !ok || name == "" {
		return nil
	}

	siteURL, ok := data["site_url"].(string)
	if !ok || siteURL == "" {
		return nil
	}

	// Get category ID from category name
	categoryName, ok := data["category"].(string)
	if !ok {
		categoryName = "Others"
	}

	categoryID, exists := constant.CategoryNameMap[categoryName]
	if !exists {
		categoryID = constant.CategoryNameMap["Others"]
	}

	// Extract other fields with defaults
	uniqueName, _ := data["unique_name"].(string)
	if uniqueName == "" {
		uniqueName = strings.ToLower(strings.ReplaceAll(name, " ", ""))
	}

	description, _ := data["description"].(string)
	logo, _ := data["logo"].(string)
	originURL, _ := data["origin_url"].(string)
	countryName, _ := data["country_name"].(string)
	supportedCountries, _ := data["supported_countries"].(string)
	trackingURL, _ := data["tracking_url"].(string)

	status := 1
	if statusVal, ok := data["status"].(int); ok {
		status = statusVal
	}

	// Parse supported countries
	var supportedCountriesArray brandEntity.StringArray
	if supportedCountries != "" {
		supportedCountriesArray = brandEntity.StringArray(strings.Split(supportedCountries, ","))
	}

	// 🚀 高性能域名匹配：检查是否为特色域名
	domainMatcher := utils.GetDomainMatcher()
	isFeatured := domainMatcher.IsFeaturedDomain(siteURL)

	return &brandEntity.Brand{
		UniqueName:         uniqueName,
		Name:               name,
		Description:        description,
		Logo:               logo,
		SiteURL:            siteURL,
		OriginURL:          originURL,
		CategoryID:         categoryID,
		CountryName:        countryName,
		Featured:           isFeatured, // 🎯 根据域名匹配设置特色标记
		SupportedCountries: supportedCountriesArray,
		Status:             status,
		TrackingURL:        trackingURL,
		TotalCoupons:       0,
		TotalDeals:         0,
		PlatformType:       platformType,
		PlatformMerchantID: merchantID,
		PlatformPriority:   priority,
	}
}

// syncAllPlatformsCoupons syncs coupon data from all configured platforms
func (s *TaskServiceImpl) syncAllPlatformsCoupons() error {
	log.Println("🔄 Starting multi-platform coupons sync...")

	ctx := &gin.Context{}

	// Get all brands for site_url mapping
	allBrands, err := s.brandRepo.GetAllBrands(ctx)
	if err != nil {
		return fmt.Errorf("failed to get all brands: %w", err)
	}

	// Create site_url to brand mapping
	siteURLToBrandMap := make(map[string]*brandEntity.Brand)
	for _, brand := range allBrands {
		if brand.SiteURL != "" {
			siteURLToBrandMap[brand.SiteURL] = brand
		}
	}

	var totalCreated int

	// Process each account
	for _, accountConfig := range constant.CouponsAccountList {
		platformType := accountConfig["type"].(string)

		var createCoupons []*couponEntity.Coupon

		accountName := accountConfig["account_name"].(string)
		log.Printf("Processing coupons account: %s (platform: %s)", accountName, platformType)

		// Get data from external API based on platform type
		var externalData []map[string]interface{}
		var apiErr error

		switch platformType {
		case constant.AccountTypeLinkbux:
			externalData, apiErr = linkbuxlib.BatchGetCoupons(accountName, accountConfig)
		default:
			log.Printf("Unsupported platform type: %s", platformType)
			continue
		}

		if apiErr != nil {
			log.Printf("Failed to get coupons from %s: %v", accountName, apiErr)
			continue
		}

		// 优化：批量获取现有 coupons 而不是逐个查询
		log.Printf("🔍 Checking existing coupons for platform %s using optimized batch query...", platformType)

		// 提取所有 coupon IDs
		couponIDs := make([]string, 0, len(externalData))
		for _, data := range externalData {
			if couponID, ok := data["id"].(string); ok && couponID != "" {
				couponIDs = append(couponIDs, couponID)
			}
		}

		// 批量查询现有 coupons
		existingCoupons, err := s.couponRepo.FindCouponsByPlatformAndCouponIDs(ctx, platformType, couponIDs)
		if err != nil {
			log.Printf("Failed to get existing coupons for platform %s: %v", platformType, err)
			continue
		}

		// 创建现有 coupons 的映射
		existingCouponMap := make(map[string]bool)
		for _, coupon := range existingCoupons {
			existingCouponMap[coupon.PlatformCouponID] = true
		}

		log.Printf("📊 Found %d existing coupons out of %d total coupons for platform %s", len(existingCoupons), len(couponIDs), platformType)

		// Process each coupon
		for _, data := range externalData {
			couponID := data["id"].(string)

			// Skip if coupon already exists (using map lookup instead of database query)
			if existingCouponMap[couponID] {
				continue
			}

			coupon := s.mapToCoupon(data, siteURLToBrandMap, platformType)
			if coupon == nil {
				continue
			}

			createCoupons = append(createCoupons, coupon)
		}

		// Batch create new coupons for this platform
		if len(createCoupons) > 0 {
			if err := s.couponRepo.BatchCreateCoupons(ctx, createCoupons); err != nil {
				log.Printf("Failed to batch create coupons for platform %s: %v", platformType, err)
				continue
			}
			log.Printf("✅ Created %d new coupons for platform %s", len(createCoupons), platformType)
			totalCreated += len(createCoupons)
		}
	}

	// Check for brands without coupons and generate random coupons
	log.Println("🔍 Checking for brands without coupons...")
	brandsWithoutCoupons, err := s.findBrandsWithoutCoupons(ctx, allBrands)
	if err != nil {
		log.Printf("⚠️ Failed to check brands without coupons: %v", err)
	} else if len(brandsWithoutCoupons) > 0 {
		log.Printf("🎲 Found %d brands without coupons, generating random coupons...", len(brandsWithoutCoupons))

		generatedCoupons, err := s.generateRandomCoupons(ctx, brandsWithoutCoupons)
		if err != nil {
			log.Printf("⚠️ Failed to generate random coupons: %v", err)
		} else if len(generatedCoupons) > 0 {
			if err := s.couponRepo.BatchCreateCoupons(ctx, generatedCoupons); err != nil {
				log.Printf("⚠️ Failed to batch create generated coupons: %v", err)
			} else {
				log.Printf("✅ Generated and created %d random coupons for %d brands", len(generatedCoupons), len(brandsWithoutCoupons))
				totalCreated += len(generatedCoupons)
			}
		}
	}

	// Update brand coupon counts
	log.Println("🔄 Updating brand coupon counts...")
	if err := s.updateBrandCouponCounts(ctx); err != nil {
		log.Printf("⚠️ Failed to update brand coupon counts: %v", err)
	} else {
		log.Println("✅ Brand coupon counts updated successfully")
	}

	log.Printf("✅ Multi-platform coupons sync completed. Total Created: %d", totalCreated)

	// 更新所有品牌的优惠券数量
	if err := s.updateAllBrandCouponCounts(ctx); err != nil {
		log.Printf("Failed to update brand coupon counts after sync: %v", err)
	}

	return nil
}

// mapToCoupon converts external data to Coupon entity
func (s *TaskServiceImpl) mapToCoupon(data map[string]interface{}, siteURLToBrandMap map[string]*brandEntity.Brand, platformType string) *couponEntity.Coupon {
	// Extract required fields
	couponID, ok := data["id"].(string)
	if !ok || couponID == "" {
		return nil
	}

	code, ok := data["code"].(string)
	if !ok || code == "" {
		return nil
	}

	name, ok := data["name"].(string)
	if !ok || name == "" {
		return nil
	}

	siteURL, ok := data["site_url"].(string)
	if !ok || siteURL == "" {
		return nil
	}

	// Find brand by site_url
	brand, exists := siteURLToBrandMap[siteURL]
	if !exists {
		log.Printf("Brand not found for site_url: %s", siteURL)
		return nil
	}

	// Use brand's category ID instead of coupon's category
	categoryID := brand.CategoryID

	// Extract other fields
	discount, _ := data["discount"].(string)
	description, _ := data["description"].(string)
	startDate, _ := data["start_date"].(string)
	endDate, _ := data["end_date"].(string)

	status := 1
	if statusVal, ok := data["status"].(int); ok {
		status = statusVal
	}

	// Parse dates
	var startDateTime, endDateTime *time.Time
	if startDate != "" {
		if t, err := time.Parse("2006-01-02", startDate); err == nil {
			startDateTime = &t
		}
	}
	if endDate != "" {
		if t, err := time.Parse("2006-01-02", endDate); err == nil {
			endDateTime = &t
		}
	}

	// 🚀 高性能域名匹配：检查是否为特色域名
	domainMatcher := utils.GetDomainMatcher()
	isFeatured := domainMatcher.IsFeaturedDomain(siteURL)

	return &couponEntity.Coupon{
		BrandID:          brand.ID,
		CategoryID:       categoryID,
		Code:             code,
		IsFeatured:       isFeatured, // 🎯 根据域名匹配设置特色标记
		IsExclusive:      isFeatured, // 🎯 特色域名的优惠券设为独家
		Discount:         discount,
		Name:             name,
		Description:      description,
		StartDate:        startDateTime,
		EndDate:          endDateTime,
		Status:           status,
		PlatformType:     platformType,
		PlatformCouponID: couponID,
	}
}

// syncAllPlatformsDeals syncs deal data from all configured platforms
func (s *TaskServiceImpl) syncAllPlatformsDeals() error {
	log.Println("🔄 Starting multi-platform deals sync...")

	ctx := &gin.Context{}

	// Get all brands for site_url mapping
	allBrands, err := s.brandRepo.GetAllBrands(ctx)
	if err != nil {
		return fmt.Errorf("failed to get all brands: %w", err)
	}

	// Create site_url to brand mapping
	siteURLToBrandMap := make(map[string]*brandEntity.Brand)
	for _, brand := range allBrands {
		if brand.SiteURL != "" {
			siteURLToBrandMap[brand.SiteURL] = brand
		}
	}

	var totalCreated int

	// Process each account
	for _, accountConfig := range constant.DealsAccountList {
		platformType := accountConfig["type"].(string)

		var createDeals []*dealEntity.Deal

		accountName := accountConfig["account_name"].(string)
		log.Printf("Processing deals account: %s (platform: %s)", accountName, platformType)

		// Get data from external API based on platform type
		var externalData []map[string]interface{}
		var apiErr error

		switch platformType {
		case constant.AccountTypeLinkbux:
			externalData, apiErr = linkbuxlib.BatchGetDeals(accountName, accountConfig)
		default:
			log.Printf("Unsupported platform type: %s", platformType)
			continue
		}

		if apiErr != nil {
			log.Printf("Failed to get deals from %s: %v", accountName, apiErr)
			continue
		}

		// 优化：批量获取现有 deals 而不是逐个查询
		log.Printf("🔍 Checking existing deals for platform %s using optimized batch query...", platformType)

		// 提取所有 deal IDs
		dealIDs := make([]string, 0, len(externalData))
		for _, data := range externalData {
			if dealID, ok := data["id"].(string); ok && dealID != "" {
				dealIDs = append(dealIDs, dealID)
			}
		}

		// 批量查询现有 deals
		existingDeals, err := s.dealRepo.FindDealsByPlatformAndDealIDs(ctx, platformType, dealIDs)
		if err != nil {
			log.Printf("Failed to get existing deals for platform %s: %v", platformType, err)
			continue
		}

		// 创建现有 deals 的映射
		existingDealMap := make(map[string]bool)
		for _, deal := range existingDeals {
			existingDealMap[deal.PlatformDealID] = true
		}

		log.Printf("📊 Found %d existing deals out of %d total deals for platform %s", len(existingDeals), len(dealIDs), platformType)

		// Process each deal
		for _, data := range externalData {
			dealID := data["id"].(string)

			// Skip if deal already exists (using map lookup instead of database query)
			if existingDealMap[dealID] {
				continue
			}

			deal := s.mapToDeal(data, siteURLToBrandMap, platformType)
			if deal == nil {
				continue
			}

			createDeals = append(createDeals, deal)
		}

		// Batch create new deals for this platform
		if len(createDeals) > 0 {
			if err := s.dealRepo.BatchCreateDeals(ctx, createDeals); err != nil {
				log.Printf("Failed to batch create deals for platform %s: %v", platformType, err)
				continue
			}
			log.Printf("✅ Created %d new deals for platform %s", len(createDeals), platformType)
			totalCreated += len(createDeals)
		}
	}

	// Update brand deal counts
	log.Println("🔄 Updating brand deal counts...")
	if err := s.updateBrandDealCounts(ctx); err != nil {
		log.Printf("⚠️ Failed to update brand deal counts: %v", err)
	} else {
		log.Println("✅ Brand deal counts updated successfully")
	}

	log.Printf("✅ Multi-platform deals sync completed. Total Created: %d", totalCreated)

	// 更新所有品牌的优惠活动数量
	if err := s.updateAllBrandDealCounts(ctx); err != nil {
		log.Printf("Failed to update brand deal counts after sync: %v", err)
	}

	return nil
}

// mapToDeal converts external data to Deal entity
func (s *TaskServiceImpl) mapToDeal(data map[string]interface{}, siteURLToBrandMap map[string]*brandEntity.Brand, platformType string) *dealEntity.Deal {
	// Extract required fields
	dealID, ok := data["id"].(string)
	if !ok || dealID == "" {
		return nil
	}

	name, ok := data["name"].(string)
	if !ok || name == "" {
		return nil
	}

	siteURL, ok := data["site_url"].(string)
	if !ok || siteURL == "" {
		return nil
	}

	// Find brand by site_url
	brand, exists := siteURLToBrandMap[siteURL]
	if !exists {
		log.Printf("Brand not found for site_url: %s", siteURL)
		return nil
	}

	// Use brand's category ID instead of deal's category
	categoryID := brand.CategoryID

	// Extract other fields
	code, _ := data["code"].(string)
	discount, _ := data["discount"].(string)
	description, _ := data["description"].(string)
	img, _ := data["img"].(string)
	trackingURL, _ := data["tracking_url"].(string)
	startDate, _ := data["start_date"].(string)
	endDate, _ := data["end_date"].(string)

	status := 1
	if statusVal, ok := data["status"].(int); ok {
		status = statusVal
	}

	// Parse dates
	var startDateTime, endDateTime *time.Time
	if startDate != "" {
		if t, err := time.Parse("2006-01-02", startDate); err == nil {
			startDateTime = &t
		}
	}
	if endDate != "" {
		if t, err := time.Parse("2006-01-02", endDate); err == nil {
			endDateTime = &t
		}
	}

	// 🚀 高性能域名匹配：检查是否为特色域名
	domainMatcher := utils.GetDomainMatcher()
	isFeatured := domainMatcher.IsFeaturedDomain(siteURL)

	return &dealEntity.Deal{
		BrandID:        brand.ID,
		CategoryID:     categoryID,
		Code:           code,
		Title:          name,
		Description:    description,
		Img:            img,
		IsHotDeal:      isFeatured, // 🎯 特色域名的 deal 设为热门
		IsFeatured:     isFeatured, // 🎯 根据域名匹配设置特色标记
		Discount:       discount,
		OriginURL:      siteURL,
		TrackingUrl:    trackingURL,
		StartDate:      startDateTime,
		EndDate:        endDateTime,
		Status:         status,
		PlatformType:   platformType,
		PlatformDealID: dealID,
	}
}

// generateRandomCoupons generates random coupons for brands without coupons
func (s *TaskServiceImpl) generateRandomCoupons(ctx *gin.Context, brandsWithoutCoupons []*brandEntity.Brand) ([]*couponEntity.Coupon, error) {
	var generatedCoupons []*couponEntity.Coupon

	// Coupon templates for different discount types
	discountTemplates := []string{
		"10% OFF", "15% OFF", "20% OFF", "25% OFF", "30% OFF",
		"$5 OFF", "$10 OFF", "$15 OFF", "$20 OFF", "$25 OFF",
		"Buy 1 Get 1 Free", "Free Shipping", "50% OFF First Order",
	}

	nameTemplates := []string{
		"Welcome Discount", "New Customer Special", "Limited Time Offer",
		"Exclusive Deal", "Flash Sale", "Weekend Special", "Holiday Savings",
		"Member Exclusive", "First Order Discount", "Special Promotion",
	}

	for _, brand := range brandsWithoutCoupons {
		// Generate 1-5 random coupons per brand
		numCoupons := rand.Intn(5) + 1

		for i := 0; i < numCoupons; i++ {
			// Generate random coupon code
			code := s.generateRandomCouponCode(brand.Name)

			// Random discount and name
			discount := discountTemplates[rand.Intn(len(discountTemplates))]
			name := nameTemplates[rand.Intn(len(nameTemplates))]

			// Random expiry date (30-365 days from now)
			daysToExpiry := rand.Intn(336) + 30 // 30-365 days
			expiryDate := time.Now().AddDate(0, 0, daysToExpiry)

			// Random start date (0-7 days from now)
			daysToStart := rand.Intn(8)
			startDate := time.Now().AddDate(0, 0, daysToStart)

			coupon := &couponEntity.Coupon{
				BrandID:          brand.ID,
				CategoryID:       brand.CategoryID,
				Code:             code,
				IsFeatured:       rand.Float32() < 0.2, // 20% chance to be featured
				IsExclusive:      rand.Float32() < 0.3, // 30% chance to be exclusive
				Discount:         discount,
				Name:             name,
				Description:      fmt.Sprintf("Get %s on your purchase at %s. Limited time offer!", discount, brand.Name),
				StartDate:        &startDate,
				EndDate:          &expiryDate,
				Status:           1,
				PlatformType:     "generated",
				PlatformCouponID: fmt.Sprintf("gen_%d_%d", brand.ID, time.Now().Unix()+int64(i)),
			}

			generatedCoupons = append(generatedCoupons, coupon)
		}
	}

	return generatedCoupons, nil
}

// generateRandomCouponCode generates a realistic coupon code
func (s *TaskServiceImpl) generateRandomCouponCode(brandName string) string {
	// Extract first few characters from brand name
	brandPrefix := ""
	cleanName := strings.ReplaceAll(strings.ToUpper(brandName), " ", "")
	if len(cleanName) >= 3 {
		brandPrefix = cleanName[:3]
	} else {
		brandPrefix = cleanName
	}

	// Common coupon code patterns
	patterns := []string{
		brandPrefix + strconv.Itoa(rand.Intn(90)+10),          // BRAND10-99
		brandPrefix + "SAVE" + strconv.Itoa(rand.Intn(50)+10), // BRANDSAVE10-59
		brandPrefix + strconv.Itoa(rand.Intn(900)+100),        // BRAND100-999
		"SAVE" + strconv.Itoa(rand.Intn(50)+10),               // SAVE10-59
		"GET" + strconv.Itoa(rand.Intn(50)+10),                // GET10-59
		"DEAL" + strconv.Itoa(rand.Intn(900)+100),             // DEAL100-999
		brandPrefix + "OFF" + strconv.Itoa(rand.Intn(50)+10),  // BRANDOFF10-59
		"WELCOME" + strconv.Itoa(rand.Intn(90)+10),            // WELCOME10-99
		brandPrefix + strconv.Itoa(rand.Intn(9000)+1000),      // BRAND1000-9999
		"SPECIAL" + strconv.Itoa(rand.Intn(900)+100),          // SPECIAL100-999
	}

	return patterns[rand.Intn(len(patterns))]
}

// findBrandsWithoutCoupons finds brands that don't have any coupons
// Optimized version: single database query instead of N+1 queries
func (s *TaskServiceImpl) findBrandsWithoutCoupons(ctx *gin.Context, allBrands []*brandEntity.Brand) ([]*brandEntity.Brand, *ecode.Error) {
	if len(allBrands) == 0 {
		return []*brandEntity.Brand{}, nil
	}

	log.Printf("🔍 Checking %d brands for coupons using optimized batch query...", len(allBrands))

	// Extract all brand IDs
	brandIDs := make([]uint64, len(allBrands))
	brandMap := make(map[uint64]*brandEntity.Brand)
	for i, brand := range allBrands {
		brandIDs[i] = uint64(brand.ID)
		brandMap[uint64(brand.ID)] = brand
	}

	// Single database query to get all coupons for all brands
	allCoupons, err := s.couponRepo.GetCouponListByBrandIDs(ctx, brandIDs)
	if err != nil {
		log.Printf("⚠️ Failed to get coupons for brands: %v", err)
		return nil, err
	}

	log.Printf("📊 Found %d total coupons across %d brands", len(allCoupons), len(allBrands))

	// Create a set of brand IDs that have coupons
	brandsWithCoupons := make(map[uint64]bool)
	for _, coupon := range allCoupons {
		brandsWithCoupons[uint64(coupon.BrandID)] = true
	}

	// Find brands without coupons
	var brandsWithoutCoupons []*brandEntity.Brand
	for brandID, brand := range brandMap {
		if !brandsWithCoupons[brandID] {
			brandsWithoutCoupons = append(brandsWithoutCoupons, brand)
		}
	}

	log.Printf("🎯 Found %d brands without coupons out of %d total brands", len(brandsWithoutCoupons), len(allBrands))

	return brandsWithoutCoupons, nil
}

// updateBrandCouponCounts updates the coupon count for all brands using batch SQL
func (s *TaskServiceImpl) updateBrandCouponCounts(ctx *gin.Context) error {
	// Use raw SQL to update all brand coupon counts in one query
	sql := `
		UPDATE brands
		SET total_coupons = COALESCE(coupon_counts.count, 0)
		FROM (
			SELECT brand_id, COUNT(*) as count
			FROM coupons
			GROUP BY brand_id
		) AS coupon_counts
		WHERE brands.id = coupon_counts.brand_id
	`

	db := persistence.GetDB()
	if err := db.WithContext(ctx).Exec(sql).Error; err != nil {
		return fmt.Errorf("failed to update brand coupon counts: %w", err)
	}

	// Also set total_coupons = 0 for brands with no coupons
	sql2 := `
		UPDATE brands
		SET total_coupons = 0
		WHERE id NOT IN (SELECT DISTINCT brand_id FROM coupons WHERE brand_id IS NOT NULL)
	`

	if err := db.WithContext(ctx).Exec(sql2).Error; err != nil {
		return fmt.Errorf("failed to reset coupon counts for brands without coupons: %w", err)
	}

	return nil
}

// updateBrandDealCounts updates the deal count for all brands using batch SQL
func (s *TaskServiceImpl) updateBrandDealCounts(ctx *gin.Context) error {
	// Use raw SQL to update all brand deal counts in one query
	sql := `
		UPDATE brands
		SET total_deals = COALESCE(deal_counts.count, 0)
		FROM (
			SELECT brand_id, COUNT(*) as count
			FROM deals
			GROUP BY brand_id
		) AS deal_counts
		WHERE brands.id = deal_counts.brand_id
	`

	db := persistence.GetDB()
	if err := db.WithContext(ctx).Exec(sql).Error; err != nil {
		return fmt.Errorf("failed to update brand deal counts: %w", err)
	}

	// Also set total_deals = 0 for brands with no deals
	sql2 := `
		UPDATE brands
		SET total_deals = 0
		WHERE id NOT IN (SELECT DISTINCT brand_id FROM deals WHERE brand_id IS NOT NULL)
	`

	if err := db.WithContext(ctx).Exec(sql2).Error; err != nil {
		return fmt.Errorf("failed to reset deal counts for brands without deals: %w", err)
	}

	return nil
}

// generateRandomCouponsForBrandsWithoutCoupons 为没有优惠券的品牌生成随机优惠券
func (s *TaskServiceImpl) generateRandomCouponsForBrandsWithoutCoupons() error {
	log.Println("🎯 Starting random coupon generation for brands without coupons...")

	ctx := &gin.Context{}

	// 获取所有激活的品牌（status=1）且优惠券数量为0的品牌
	allBrands, err := s.brandRepo.GetAllBrands(ctx)
	if err != nil {
		return fmt.Errorf("failed to get all brands: %w", err)
	}

	// 找出激活且没有优惠券的品牌
	var brandsWithoutCoupons []*brandEntity.Brand
	for _, brand := range allBrands {
		if brand.Status == 1 && brand.TotalCoupons == 0 {
			brandsWithoutCoupons = append(brandsWithoutCoupons, brand)
		}
	}

	log.Printf("Found %d brands without coupons out of %d total active brands", len(brandsWithoutCoupons), len(allBrands))

	if len(brandsWithoutCoupons) == 0 {
		log.Println("✅ All brands already have coupons")
		return nil
	}

	// 为每个没有优惠券的品牌生成随机优惠券
	var newCoupons []*couponEntity.Coupon
	for _, brand := range brandsWithoutCoupons {
		coupons := s.generateRandomCouponsForBrand(brand)
		newCoupons = append(newCoupons, coupons...)
	}

	// 批量创建优惠券
	if len(newCoupons) > 0 {
		if err := s.couponRepo.BatchCreateCoupons(ctx, newCoupons); err != nil {
			return fmt.Errorf("failed to batch create random coupons: %w", err)
		}
		log.Printf("✅ Generated %d random coupons for %d brands", len(newCoupons), len(brandsWithoutCoupons))

		// 更新这些品牌的优惠券数量
		brandCouponCount := make(map[uint]int)
		for _, coupon := range newCoupons {
			brandCouponCount[coupon.BrandID]++
		}

		for brandID, count := range brandCouponCount {
			if err := s.brandRepo.UpdateBrandCouponCount(ctx, brandID, count); err != nil {
				log.Printf("Failed to update coupon count for brand %d: %v", brandID, err)
			}
		}
	}

	return nil
}

// generateRandomCouponsForBrand 为单个品牌生成随机优惠券
func (s *TaskServiceImpl) generateRandomCouponsForBrand(brand *brandEntity.Brand) []*couponEntity.Coupon {
	// 随机生成1-5个优惠券
	couponCount := rand.Intn(5) + 1

	var coupons []*couponEntity.Coupon
	for i := 0; i < couponCount; i++ {
		coupon := &couponEntity.Coupon{
			BrandID:          brand.ID,
			CategoryID:       brand.CategoryID,
			Code:             s.generateRealisticCouponCode(),
			IsFeatured:       rand.Float32() < 0.2, // 20% 概率为特色优惠券
			IsExclusive:      rand.Float32() < 0.3, // 30% 概率为独家优惠券
			Discount:         s.generateRealisticDiscount(),
			Name:             s.generateRealisticCouponName(),
			Description:      s.generateRealisticCouponDescription(),
			StartDate:        s.generateRealisticStartDate(),
			EndDate:          s.generateRealisticEndDate(),
			Status:           1,
			PlatformType:     "generated", // 标记为生成的优惠券
			PlatformCouponID: fmt.Sprintf("gen_%d_%d_%d", brand.ID, time.Now().Unix(), i),
		}
		coupons = append(coupons, coupon)
	}

	return coupons
}

// generateRealisticCouponCode 生成真实的优惠券代码
func (s *TaskServiceImpl) generateRealisticCouponCode() string {
	// 常见的优惠券代码模式
	patterns := []string{
		"SAVE%d",
		"GET%d",
		"DEAL%d",
		"OFFER%d",
		"DISCOUNT%d",
		"WELCOME%d",
		"FIRST%d",
		"SPECIAL%d",
		"EXTRA%d",
		"BONUS%d",
	}

	pattern := patterns[rand.Intn(len(patterns))]
	number := rand.Intn(50) + 10 // 10-59

	return fmt.Sprintf(pattern, number)
}

// generateRealisticDiscount 生成真实的折扣描述
func (s *TaskServiceImpl) generateRealisticDiscount() string {
	discountTypes := []string{
		"%d%% Off",
		"$%d Off",
		"Up to %d%% Off",
		"Save %d%%",
		"Get %d%% Discount",
		"%d%% Off Sitewide",
		"Extra %d%% Off",
	}

	discountType := discountTypes[rand.Intn(len(discountTypes))]

	if strings.Contains(discountType, "%%") {
		// 百分比折扣：5-50%
		percentage := []int{5, 10, 15, 20, 25, 30, 35, 40, 45, 50}
		value := percentage[rand.Intn(len(percentage))]
		return fmt.Sprintf(discountType, value)
	} else {
		// 金额折扣：$5-$100
		amounts := []int{5, 10, 15, 20, 25, 30, 50, 75, 100}
		value := amounts[rand.Intn(len(amounts))]
		return fmt.Sprintf(discountType, value)
	}
}

// generateRealisticCouponName 生成真实的优惠券名称
func (s *TaskServiceImpl) generateRealisticCouponName() string {
	names := []string{
		"Welcome Discount",
		"First Time Buyer Offer",
		"Seasonal Sale",
		"Flash Deal",
		"Limited Time Offer",
		"Exclusive Discount",
		"Special Promotion",
		"Weekend Sale",
		"Holiday Special",
		"Member Exclusive",
		"New Customer Deal",
		"Clearance Sale",
		"End of Season Sale",
		"Black Friday Deal",
		"Cyber Monday Special",
		"Summer Sale",
		"Winter Clearance",
		"Spring Promotion",
		"Fall Special",
		"Anniversary Sale",
	}

	return names[rand.Intn(len(names))]
}

// generateRealisticCouponDescription 生成真实的优惠券描述
func (s *TaskServiceImpl) generateRealisticCouponDescription() string {
	descriptions := []string{
		"Save on your favorite items with this exclusive discount.",
		"Limited time offer - don't miss out on these amazing savings!",
		"Get the best deals on top-quality products.",
		"Exclusive discount for new customers only.",
		"Special promotion for a limited time.",
		"Enjoy significant savings on your next purchase.",
		"Take advantage of this incredible offer while it lasts.",
		"Perfect opportunity to save on premium products.",
		"Don't wait - this deal won't last long!",
		"Exceptional value for money with this special offer.",
		"Treat yourself to something special at a great price.",
		"Amazing discounts on selected items.",
		"Your chance to save big on quality products.",
		"Unlock exclusive savings with this special code.",
		"Limited quantity available - act fast!",
	}

	return descriptions[rand.Intn(len(descriptions))]
}

// generateRealisticStartDate 生成真实的开始日期
func (s *TaskServiceImpl) generateRealisticStartDate() *time.Time {
	// 开始日期：今天或最近几天
	daysAgo := rand.Intn(7) // 0-6天前
	startDate := time.Now().AddDate(0, 0, -daysAgo)
	return &startDate
}

// generateRealisticEndDate 生成真实的结束日期
func (s *TaskServiceImpl) generateRealisticEndDate() *time.Time {
	// 结束日期：7-90天后
	daysLater := rand.Intn(84) + 7 // 7-90天后
	endDate := time.Now().AddDate(0, 0, daysLater)
	return &endDate
}

// updateAllBrandCounts 更新所有品牌的优惠券和优惠活动数量
func (s *TaskServiceImpl) updateAllBrandCounts(ctx *gin.Context) error {
	log.Println("🔄 Starting to update all brand counts...")

	// 更新优惠券数量
	if err := s.updateAllBrandCouponCounts(ctx); err != nil {
		return fmt.Errorf("failed to update coupon counts: %w", err)
	}

	// 更新优惠活动数量
	if err := s.updateAllBrandDealCounts(ctx); err != nil {
		return fmt.Errorf("failed to update deal counts: %w", err)
	}

	log.Println("✅ All brand counts updated successfully")
	return nil
}

// updateAllBrandCouponCounts 更新所有品牌的优惠券数量
func (s *TaskServiceImpl) updateAllBrandCouponCounts(ctx *gin.Context) error {
	log.Println("🔄 Updating brand coupon counts...")

	// 获取所有品牌
	allBrands, err := s.brandRepo.GetAllBrands(ctx)
	if err != nil {
		return fmt.Errorf("failed to get all brands: %w", err)
	}

	// 批量获取所有品牌的优惠券数量
	brandIDs := make([]uint64, len(allBrands))
	for i, brand := range allBrands {
		brandIDs[i] = uint64(brand.ID)
	}

	coupons, err := s.couponRepo.GetCouponListByBrandIDs(ctx, brandIDs)
	if err != nil {
		return fmt.Errorf("failed to get coupons: %w", err)
	}

	// 统计每个品牌的优惠券数量
	brandCouponCount := make(map[uint]int)
	for _, coupon := range coupons {
		if coupon.Status == 1 { // 只统计激活的优惠券
			brandCouponCount[coupon.BrandID]++
		}
	}

	// 更新每个品牌的优惠券数量
	var updateCount int
	for _, brand := range allBrands {
		count := brandCouponCount[brand.ID]
		if brand.TotalCoupons != count {
			if err := s.brandRepo.UpdateBrandCouponCount(ctx, brand.ID, count); err != nil {
				log.Printf("Failed to update coupon count for brand %d: %v", brand.ID, err)
				continue
			}
			updateCount++
		}
	}

	log.Printf("✅ Updated coupon counts for %d brands", updateCount)
	return nil
}

// updateAllBrandDealCounts 更新所有品牌的优惠活动数量
func (s *TaskServiceImpl) updateAllBrandDealCounts(ctx *gin.Context) error {
	log.Println("🔄 Updating brand deal counts...")

	// 获取所有品牌
	allBrands, err := s.brandRepo.GetAllBrands(ctx)
	if err != nil {
		return fmt.Errorf("failed to get all brands: %w", err)
	}

	// 批量获取所有品牌的优惠活动数量
	brandIDs := make([]uint64, len(allBrands))
	for i, brand := range allBrands {
		brandIDs[i] = uint64(brand.ID)
	}

	deals, err := s.dealRepo.GetDealListByBrandIDs(ctx, brandIDs)
	if err != nil {
		return fmt.Errorf("failed to get deals: %w", err)
	}

	// 统计每个品牌的优惠活动数量
	brandDealCount := make(map[uint]int)
	for _, deal := range deals {
		if deal.Status == 1 { // 只统计激活的优惠活动
			brandDealCount[deal.BrandID]++
		}
	}

	// 更新每个品牌的优惠活动数量
	var updateCount int
	for _, brand := range allBrands {
		count := brandDealCount[brand.ID]
		if brand.TotalDeals != count {
			if err := s.brandRepo.UpdateBrandDealCount(ctx, brand.ID, count); err != nil {
				log.Printf("Failed to update deal count for brand %d: %v", brand.ID, err)
				continue
			}
			updateCount++
		}
	}

	log.Printf("✅ Updated deal counts for %d brands", updateCount)
	return nil
}
