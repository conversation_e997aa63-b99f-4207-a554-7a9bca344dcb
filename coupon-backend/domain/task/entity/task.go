package entity

import "time"

// TaskStatus task status
type TaskStatus int

const (
	TaskStatusPending   TaskStatus = 0 // Pending
	TaskStatusRunning   TaskStatus = 1 // Running
	TaskStatusCompleted TaskStatus = 2 // Completed
	TaskStatusFailed    TaskStatus = 3 // Failed
)

// TaskType task type
type TaskType string

const (
	TaskTypeSyncBrand   TaskType = "sync_brand"   // Sync brands
	TaskTypeSyncCoupon  TaskType = "sync_coupon"  // Sync coupons
	TaskTypeSyncDeal    TaskType = "sync_deal"    // Sync deals
	TaskTypeWarmupCache TaskType = "warmup_cache" // Cache warmup
)

// Task task entity
type Task struct {
	ID             uint       `json:"id" gorm:"primaryKey"`
	CreatedAt      time.Time  `json:"created_at"`
	UpdatedAt      time.Time  `json:"updated_at"`
	Type           TaskType   `json:"type" gorm:"type:varchar(50);not null"`
	Status         TaskStatus `json:"status" gorm:"default:0;not null"`
	StartTime      *time.Time `json:"start_time"`
	EndTime        *time.Time `json:"end_time"`
	Duration       int64      `json:"duration"` // Execution duration (milliseconds)
	Message        string     `json:"message" gorm:"type:text"`
	ErrorMsg       string     `json:"error_msg" gorm:"type:text"`
	ProcessedCount int        `json:"processed_count" gorm:"default:0"` // Processed count
	SuccessCount   int        `json:"success_count" gorm:"default:0"`   // Success count
	FailedCount    int        `json:"failed_count" gorm:"default:0"`    // Failed count
}

// TaskLog task log
type TaskLog struct {
	ID        uint      `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time `json:"created_at"`
	TaskID    uint      `json:"task_id" gorm:"not null"`
	Level     string    `json:"level" gorm:"type:varchar(10);not null"` // INFO, WARN, ERROR
	Message   string    `json:"message" gorm:"type:text"`
	Data      string    `json:"data" gorm:"type:text"` // JSON format additional data
}
