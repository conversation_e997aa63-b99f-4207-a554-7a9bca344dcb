package repository

import (
	"context"
	"coupon-backend/domain/subscription/entity"
)

// SubscriptionRepo 订阅仓储接口
type SubscriptionRepo interface {
	// Create 创建订阅
	Create(ctx context.Context, subscription *entity.Subscription) error

	// GetByEmail 根据邮箱获取订阅信息
	GetByEmail(ctx context.Context, email string) (*entity.Subscription, error)

	// UpdateStatus 更新订阅状态
	UpdateStatus(ctx context.Context, email string, status int) error

	// List 获取订阅列表（分页）
	List(ctx context.Context, condition map[string]interface{}) ([]*entity.Subscription, int64, error)

	// Delete 删除订阅（物理删除）
	Delete(ctx context.Context, email string) error
}
