package service

import (
	"context"
	"coupon-backend/domain/subscription/entity"
	"coupon-backend/domain/subscription/repository"
	"coupon-backend/infra/ecode"
	"regexp"
	"strings"
	"time"
)

// SubscriptionService 订阅服务接口
type SubscriptionService interface {
	// Subscribe 订阅邮箱
	Subscribe(ctx context.Context, email, source, ipAddress, userAgent string) error

	// Unsubscribe 取消订阅
	Unsubscribe(ctx context.Context, email string) error

	// GetSubscription 获取订阅信息
	GetSubscription(ctx context.Context, email string) (*entity.Subscription, error)

	// ListSubscriptions 获取订阅列表
	ListSubscriptions(ctx context.Context, condition map[string]interface{}) ([]*entity.Subscription, int64, error)
}

type subscriptionService struct {
	subscriptionRepo repository.SubscriptionRepo
}

// NewSubscriptionService 创建订阅服务
func NewSubscriptionService(subscriptionRepo repository.SubscriptionRepo) SubscriptionService {
	return &subscriptionService{
		subscriptionRepo: subscriptionRepo,
	}
}

// Subscribe 订阅邮箱
func (s *subscriptionService) Subscribe(ctx context.Context, email, source, ipAddress, userAgent string) error {
	// 验证邮箱格式
	if !isValidEmail(email) {
		return ecode.New(ecode.ErrInvalidParameter.Code, "invalid email format")
	}

	// 标准化邮箱（转小写，去空格）
	email = strings.ToLower(strings.TrimSpace(email))

	// 检查是否已存在
	existing, err := s.subscriptionRepo.GetByEmail(ctx, email)
	if err != nil && !ecode.IsNotFound(err) {
		return err
	}

	if existing != nil {
		// 如果已存在且状态为活跃，直接返回成功
		if existing.Status == entity.SubscriptionStatusActive {
			return nil
		}
		// 如果已存在但状态为非活跃，更新状态为活跃
		return s.subscriptionRepo.UpdateStatus(ctx, email, entity.SubscriptionStatusActive)
	}

	// 创建新订阅
	subscription := &entity.Subscription{
		Email:     email,
		Status:    entity.SubscriptionStatusActive,
		Source:    source,
		IPAddress: ipAddress,
		UserAgent: userAgent,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	return s.subscriptionRepo.Create(ctx, subscription)
}

// Unsubscribe 取消订阅
func (s *subscriptionService) Unsubscribe(ctx context.Context, email string) error {
	email = strings.ToLower(strings.TrimSpace(email))

	// 检查订阅是否存在
	_, err := s.subscriptionRepo.GetByEmail(ctx, email)
	if err != nil {
		return err
	}

	// 更新状态为非活跃
	return s.subscriptionRepo.UpdateStatus(ctx, email, entity.SubscriptionStatusInactive)
}

// GetSubscription 获取订阅信息
func (s *subscriptionService) GetSubscription(ctx context.Context, email string) (*entity.Subscription, error) {
	email = strings.ToLower(strings.TrimSpace(email))
	return s.subscriptionRepo.GetByEmail(ctx, email)
}

// ListSubscriptions 获取订阅列表
func (s *subscriptionService) ListSubscriptions(ctx context.Context, condition map[string]interface{}) ([]*entity.Subscription, int64, error) {
	return s.subscriptionRepo.List(ctx, condition)
}

// isValidEmail 验证邮箱格式
func isValidEmail(email string) bool {
	// 简单的邮箱格式验证
	emailRegex := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
	return emailRegex.MatchString(email)
}
