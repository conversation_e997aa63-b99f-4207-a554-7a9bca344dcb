package entity

import (
	"time"
)

// Subscription 邮箱订阅实体
type Subscription struct {
	ID          int       `json:"id" gorm:"primaryKey;autoIncrement"`
	Email       string    `json:"email" gorm:"type:varchar(255);uniqueIndex;not null;comment:邮箱地址"`
	Status      int       `json:"status" gorm:"default:1;comment:状态 1-已订阅 0-已取消"`
	Source      string    `json:"source" gorm:"type:varchar(50);default:'website';comment:订阅来源"`
	IPAddress   string    `json:"ip_address" gorm:"type:varchar(50);comment:订阅时的IP地址"`
	UserAgent   string    `json:"user_agent" gorm:"type:text;comment:用户代理"`
	Country     string    `json:"country" gorm:"type:varchar(100);comment:国家"`
	CountryCode string    `json:"country_code" gorm:"type:varchar(10);comment:国家代码"`
	Region      string    `json:"region" gorm:"type:varchar(100);comment:地区"`
	City        string    `json:"city" gorm:"type:varchar(100);comment:城市"`
	Timezone    string    `json:"timezone" gorm:"type:varchar(50);comment:时区"`
	ISP         string    `json:"isp" gorm:"type:varchar(200);comment:网络服务提供商"`
	CreatedAt   time.Time `json:"created_at" gorm:"autoCreateTime;comment:创建时间"`
	UpdatedAt   time.Time `json:"updated_at" gorm:"autoUpdateTime;comment:更新时间"`
}

// TableName 指定表名
func (Subscription) TableName() string {
	return "subscriptions"
}

// SubscriptionStatus 订阅状态常量
const (
	SubscriptionStatusActive   = 1 // 已订阅
	SubscriptionStatusInactive = 0 // 已取消
)
