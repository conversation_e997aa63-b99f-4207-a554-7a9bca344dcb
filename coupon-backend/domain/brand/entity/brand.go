package entity

import (
	"database/sql/driver"
	"strings"
	"time"

	"github.com/lib/pq"
)

// StringArray is a custom type for PostgreSQL string arrays
type StringArray []string

// Scan implements the Scanner interface for database/sql
func (a *StringArray) Scan(value interface{}) error {
	if value == nil {
		*a = nil
		return nil
	}

	switch v := value.(type) {
	case []byte:
		// Handle PostgreSQL array format like {item1,item2,item3}
		str := string(v)
		if str == "{}" || str == "" {
			*a = StringArray{}
			return nil
		}
		// Remove braces and split by comma
		str = strings.Trim(str, "{}")
		if str == "" {
			*a = StringArray{}
			return nil
		}
		*a = StringArray(strings.Split(str, ","))
		return nil
	case string:
		// Handle string representation
		if v == "{}" || v == "" {
			*a = StringArray{}
			return nil
		}
		v = strings.Trim(v, "{}")
		if v == "" {
			*a = StringArray{}
			return nil
		}
		*a = StringArray(strings.Split(v, ","))
		return nil
	default:
		return pq.Array(a).Scan(value)
	}
}

// Value implements the driver Valuer interface
func (a StringArray) Value() (driver.Value, error) {
	if a == nil {
		return nil, nil
	}
	return pq.Array(a).Value()
}

// Brand represents a brand entity
type Brand struct {
	ID                 uint        `json:"id" gorm:"primaryKey"`
	CreatedAt          time.Time   `json:"created_at"`
	UpdatedAt          time.Time   `json:"updated_at"`
	UniqueName         string      `json:"unique_name" gorm:"size:500;uniqueIndex;not null"`
	Name               string      `json:"name" gorm:"size:100;not null"`
	Description        string      `json:"description" gorm:"type:text"`
	Logo               string      `json:"logo" gorm:"type:varchar(1024)"`
	SiteURL            string      `json:"site_url" gorm:"type:varchar(1024)"`
	OriginURL          string      `json:"origin_url" gorm:"type:varchar(1024)"`
	CategoryID         uint        `gorm:"not null" json:"category_id"`                            // 分类ID
	CountryName        string      `gorm:"size:255" json:"country"`                                // 国家名字
	Featured           bool        `gorm:"default:false;not null" json:"featured"`                 // 是否推荐：如果有单，则为 true
	SupportedCountries StringArray `gorm:"type:varchar[];default:'{}'" json:"supported_countries"` // 支持返利的国家（仅仅用于展示，实际没关系）
	Status             int         `json:"status" gorm:"default:1;not null"`
	TrackingURL        string      `json:"tracking_url" gorm:"type:text"`
	TotalCoupons       int         `json:"total_coupons" gorm:"default:0"`
	TotalDeals         int         `json:"total_deals" gorm:"default:0"`

	// 以下内容绝不展示给前端
	PlatformType       string `gorm:"type:varchar(63);not null" json:"platform_type"`        // 平台类型：cj、awin
	PlatformMerchantID string `gorm:"type:varchar(63);not null" json:"platform_merchant_id"` // 平台商家ID
	PlatformPriority   int    `json:"platform_priority" gorm:"default:999"`                  // 平台优先级：数字越小优先级越高
}
