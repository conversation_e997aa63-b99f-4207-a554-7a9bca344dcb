-- Add platform_priority column to brands table
-- This migration adds the platform_priority field to support brand deduplication

ALTER TABLE brands ADD COLUMN IF NOT EXISTS platform_priority INTEGER DEFAULT 999;

-- Add index for better performance on deduplication queries
CREATE INDEX IF NOT EXISTS idx_brands_site_url ON brands(site_url);
CREATE INDEX IF NOT EXISTS idx_brands_platform_priority ON brands(platform_priority);
CREATE INDEX IF NOT EXISTS idx_brands_status_site_url ON brands(status, site_url);

-- Update existing records to have default priority
UPDATE brands SET platform_priority = 999 WHERE platform_priority IS NULL;

-- Add comment to document the field
COMMENT ON COLUMN brands.platform_priority IS 'Platform priority for deduplication: lower number = higher priority';
