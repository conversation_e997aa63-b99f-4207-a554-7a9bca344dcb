-- 性能优化索引

-- 优惠券表优化索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_coupons_status_featured ON coupons(status, is_featured) WHERE status = 1;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_coupons_status_exclusive ON coupons(status, is_exclusive) WHERE status = 1;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_coupons_brand_status ON coupons(brand_id, status) WHERE status = 1;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_coupons_category_status ON coupons(category_id, status) WHERE status = 1;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_coupons_created_at_id ON coupons(created_at DESC, id ASC);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_coupons_updated_at_id ON coupons(updated_at DESC, id ASC);

-- 优惠活动表优化索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_deals_status_featured ON deals(status, is_featured) WHERE status = 1;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_deals_status_hot ON deals(status, is_hot_deal) WHERE status = 1;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_deals_brand_status ON deals(brand_id, status) WHERE status = 1;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_deals_category_status ON deals(category_id, status) WHERE status = 1;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_deals_created_at_id ON deals(created_at DESC, id ASC);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_deals_updated_at_id ON deals(updated_at DESC, id ASC);

-- 商家表优化索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_brands_featured_status ON brands(featured, status) WHERE status = 1;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_brands_category_status ON brands(category_id, status) WHERE status = 1;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_brands_created_at_id ON brands(created_at DESC, id ASC);

-- 分类表优化索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_categories_slug ON categories(slug);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_categories_name ON categories(name);

-- 全文搜索索引（如果需要）
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_coupons_name_gin ON coupons USING gin(to_tsvector('english', name));
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_coupons_description_gin ON coupons USING gin(to_tsvector('english', description));
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_deals_title_gin ON deals USING gin(to_tsvector('english', title));
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_deals_description_gin ON deals USING gin(to_tsvector('english', description));
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_brands_name_gin ON brands USING gin(to_tsvector('english', name));

-- 复合查询优化索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_coupons_complex_query ON coupons(status, category_id, brand_id, is_featured, created_at DESC, id ASC) WHERE status = 1;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_deals_complex_query ON deals(status, category_id, brand_id, is_featured, is_hot_deal, created_at DESC, id ASC) WHERE status = 1;

-- 统计查询优化
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_coupons_count_by_brand ON coupons(brand_id) WHERE status = 1;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_coupons_count_by_category ON coupons(category_id) WHERE status = 1;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_deals_count_by_brand ON deals(brand_id) WHERE status = 1;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_deals_count_by_category ON deals(category_id) WHERE status = 1;
