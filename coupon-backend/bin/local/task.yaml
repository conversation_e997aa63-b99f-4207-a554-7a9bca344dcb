# 本地开发环境 - 定时任务配置
database:
  host: localhost
  port: 5432
  user: postgres
  password: postgres
  db_name: maxcoupon
  ssl_mode: disable
  max_idle_conns: 5
  max_open_conns: 20
  max_lifetime: 3600s

redis:
  host: localhost
  port: 6379
  password: ""
  db: 0
  pool_size: 5
  min_idle_conns: 2
  dial_timeout: 5s
  read_timeout: 3s
  write_timeout: 3s

log:
  level: debug
  filename: logs/task_local.log
  max_size: 100
  max_age: 30
  compress: true

# 任务调度配置
task:
  # 商家同步任务 - 每天凌晨2点
  brand_sync_cron: "0 2 * * *"
  # 优惠券同步任务 - 每4小时
  coupon_sync_cron: "0 */4 * * *"
  # 优惠活动同步任务 - 每6小时
  deal_sync_cron: "0 */6 * * *"
  # 任务超时时间
  task_timeout: 1800s  # 30分钟