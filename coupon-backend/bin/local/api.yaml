# 本地开发环境配置
server:
  port: 8080
  mode: debug
  read_timeout: 60s
  write_timeout: 60s

database:
  host: localhost
  port: 5432
  user: postgres
  password: postgres
  db_name: maxcoupon
  ssl_mode: disable
  max_idle_conns: 10
  max_open_conns: 100
  max_lifetime: 3600s

redis:
  host: localhost
  port: 6379
  password: ""
  db: 0
  pool_size: 10
  min_idle_conns: 5
  dial_timeout: 5s
  read_timeout: 3s
  write_timeout: 3s

log:
  level: debug
  filename: logs/app_local.log
  max_size: 100
  max_age: 30
  compress: true

website:
  domain: gocoupons.org
  url: https://gocoupons.org
