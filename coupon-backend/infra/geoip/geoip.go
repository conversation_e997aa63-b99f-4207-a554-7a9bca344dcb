package geoip

import (
	"encoding/json"
	"fmt"
	"io"
	"net"
	"net/http"
	"strings"
	"sync"
	"time"
)

// IPInfo IP地理位置信息
type IPInfo struct {
	IP          string `json:"ip"`
	Country     string `json:"country"`
	CountryCode string `json:"country_code"`
	Region      string `json:"region"`
	RegionCode  string `json:"region_code"`
	City        string `json:"city"`
	Timezone    string `json:"timezone"`
	ISP         string `json:"isp"`
	Latitude    string `json:"latitude"`
	Longitude   string `json:"longitude"`
}

// GeoIPService IP地理位置服务
type GeoIPService struct {
	cache    map[string]*IPInfo
	cacheMu  sync.RWMutex
	client   *http.Client
	fallback []string // 备用免费API列表
}

// NewGeoIPService 创建IP地理位置服务
func NewGeoIPService() *GeoIPService {
	return &GeoIPService{
		cache: make(map[string]*IPInfo),
		client: &http.Client{
			Timeout: 5 * time.Second,
		},
		// 免费API列表（按优先级排序）
		fallback: []string{
			"http://ip-api.com/json/%s?fields=status,message,country,countryCode,region,regionName,city,timezone,isp,lat,lon,query",
			"https://ipapi.co/%s/json/",
			"http://www.geoplugin.net/json.gp?ip=%s",
		},
	}
}

// GetIPInfo 获取IP地理位置信息
func (g *GeoIPService) GetIPInfo(ip string) *IPInfo {
	// 检查是否为本地IP
	if g.isLocalIP(ip) {
		return &IPInfo{
			IP:          ip,
			Country:     "Local",
			CountryCode: "LOCAL",
			Region:      "Local",
			City:        "Local",
			ISP:         "Local Network",
		}
	}

	// 检查缓存
	g.cacheMu.RLock()
	if info, exists := g.cache[ip]; exists {
		g.cacheMu.RUnlock()
		return info
	}
	g.cacheMu.RUnlock()

	// 尝试从免费API获取
	info := g.fetchFromAPIs(ip)
	if info != nil {
		// 缓存结果
		g.cacheMu.Lock()
		g.cache[ip] = info
		g.cacheMu.Unlock()
	}

	return info
}

// fetchFromAPIs 从免费API获取IP信息
func (g *GeoIPService) fetchFromAPIs(ip string) *IPInfo {
	for _, apiURL := range g.fallback {
		if info := g.fetchFromAPI(fmt.Sprintf(apiURL, ip), ip); info != nil {
			return info
		}
	}
	
	// 如果所有API都失败，返回基本信息
	return &IPInfo{
		IP:          ip,
		Country:     "Unknown",
		CountryCode: "UNKNOWN",
		Region:      "Unknown",
		City:        "Unknown",
		ISP:         "Unknown",
	}
}

// fetchFromAPI 从单个API获取信息
func (g *GeoIPService) fetchFromAPI(url, ip string) *IPInfo {
	resp, err := g.client.Get(url)
	if err != nil {
		return nil
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil
	}

	// 根据不同API解析响应
	if strings.Contains(url, "ip-api.com") {
		return g.parseIPAPI(body, ip)
	} else if strings.Contains(url, "ipapi.co") {
		return g.parseIPAPICo(body, ip)
	} else if strings.Contains(url, "geoplugin.net") {
		return g.parseGeoPlugin(body, ip)
	}

	return nil
}

// parseIPAPI 解析ip-api.com响应
func (g *GeoIPService) parseIPAPI(body []byte, ip string) *IPInfo {
	var response struct {
		Status      string  `json:"status"`
		Country     string  `json:"country"`
		CountryCode string  `json:"countryCode"`
		Region      string  `json:"region"`
		RegionName  string  `json:"regionName"`
		City        string  `json:"city"`
		Timezone    string  `json:"timezone"`
		ISP         string  `json:"isp"`
		Lat         float64 `json:"lat"`
		Lon         float64 `json:"lon"`
		Query       string  `json:"query"`
	}

	if err := json.Unmarshal(body, &response); err != nil {
		return nil
	}

	if response.Status != "success" {
		return nil
	}

	return &IPInfo{
		IP:          ip,
		Country:     response.Country,
		CountryCode: response.CountryCode,
		Region:      response.RegionName,
		RegionCode:  response.Region,
		City:        response.City,
		Timezone:    response.Timezone,
		ISP:         response.ISP,
		Latitude:    fmt.Sprintf("%.6f", response.Lat),
		Longitude:   fmt.Sprintf("%.6f", response.Lon),
	}
}

// parseIPAPICo 解析ipapi.co响应
func (g *GeoIPService) parseIPAPICo(body []byte, ip string) *IPInfo {
	var response struct {
		IP          string `json:"ip"`
		Country     string `json:"country_name"`
		CountryCode string `json:"country_code"`
		Region      string `json:"region"`
		City        string `json:"city"`
		Timezone    string `json:"timezone"`
		ISP         string `json:"org"`
		Latitude    string `json:"latitude"`
		Longitude   string `json:"longitude"`
	}

	if err := json.Unmarshal(body, &response); err != nil {
		return nil
	}

	return &IPInfo{
		IP:          ip,
		Country:     response.Country,
		CountryCode: response.CountryCode,
		Region:      response.Region,
		City:        response.City,
		Timezone:    response.Timezone,
		ISP:         response.ISP,
		Latitude:    response.Latitude,
		Longitude:   response.Longitude,
	}
}

// parseGeoPlugin 解析geoplugin.net响应
func (g *GeoIPService) parseGeoPlugin(body []byte, ip string) *IPInfo {
	var response struct {
		Country     string `json:"geoplugin_countryName"`
		CountryCode string `json:"geoplugin_countryCode"`
		Region      string `json:"geoplugin_regionName"`
		City        string `json:"geoplugin_city"`
		Timezone    string `json:"geoplugin_timezone"`
		Latitude    string `json:"geoplugin_latitude"`
		Longitude   string `json:"geoplugin_longitude"`
	}

	if err := json.Unmarshal(body, &response); err != nil {
		return nil
	}

	return &IPInfo{
		IP:          ip,
		Country:     response.Country,
		CountryCode: response.CountryCode,
		Region:      response.Region,
		City:        response.City,
		Timezone:    response.Timezone,
		ISP:         "Unknown",
		Latitude:    response.Latitude,
		Longitude:   response.Longitude,
	}
}

// isLocalIP 检查是否为本地IP
func (g *GeoIPService) isLocalIP(ip string) bool {
	if ip == "" || ip == "::1" || ip == "127.0.0.1" {
		return true
	}

	parsedIP := net.ParseIP(ip)
	if parsedIP == nil {
		return true
	}

	// 检查私有IP范围
	privateRanges := []string{
		"10.0.0.0/8",
		"**********/12",
		"***********/16",
		"***********/16", // 链路本地地址
		"fc00::/7",       // IPv6 私有地址
	}

	for _, cidr := range privateRanges {
		_, network, err := net.ParseCIDR(cidr)
		if err != nil {
			continue
		}
		if network.Contains(parsedIP) {
			return true
		}
	}

	return false
}

// GetCountryFromIP 快速获取国家信息
func (g *GeoIPService) GetCountryFromIP(ip string) string {
	info := g.GetIPInfo(ip)
	if info != nil {
		return info.Country
	}
	return "Unknown"
}

// GetCountryCodeFromIP 快速获取国家代码
func (g *GeoIPService) GetCountryCodeFromIP(ip string) string {
	info := g.GetIPInfo(ip)
	if info != nil {
		return info.CountryCode
	}
	return "UNKNOWN"
}

// ClearCache 清理缓存
func (g *GeoIPService) ClearCache() {
	g.cacheMu.Lock()
	defer g.cacheMu.Unlock()
	g.cache = make(map[string]*IPInfo)
}
