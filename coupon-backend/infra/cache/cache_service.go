package cache

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/redis/go-redis/v9"
)

type CacheService interface {
	Get(ctx context.Context, key string, dest interface{}) error
	Set(ctx context.Context, key string, value interface{}, expiration time.Duration) error
	Delete(ctx context.Context, key string) error
	GetMultiple(ctx context.Context, keys []string) (map[string]string, error)
	SetMultiple(ctx context.Context, data map[string]interface{}, expiration time.Duration) error
	Exists(ctx context.Context, key string) (bool, error)
}

type CacheServiceImpl struct {
	client *redis.Client
}

func NewCacheService(client *redis.Client) CacheService {
	return &CacheServiceImpl{
		client: client,
	}
}

func (c *CacheServiceImpl) Get(ctx context.Context, key string, dest interface{}) error {
	val, err := c.client.Get(ctx, key).Result()
	if err != nil {
		return err
	}
	return json.Unmarshal([]byte(val), dest)
}

func (c *CacheServiceImpl) Set(ctx context.Context, key string, value interface{}, expiration time.Duration) error {
	data, err := json.Marshal(value)
	if err != nil {
		return err
	}
	return c.client.Set(ctx, key, data, expiration).Err()
}

func (c *CacheServiceImpl) Delete(ctx context.Context, key string) error {
	return c.client.Del(ctx, key).Err()
}

func (c *CacheServiceImpl) GetMultiple(ctx context.Context, keys []string) (map[string]string, error) {
	if len(keys) == 0 {
		return make(map[string]string), nil
	}

	vals, err := c.client.MGet(ctx, keys...).Result()
	if err != nil {
		return nil, err
	}

	result := make(map[string]string)
	for i, val := range vals {
		if val != nil {
			result[keys[i]] = val.(string)
		}
	}
	return result, nil
}

func (c *CacheServiceImpl) SetMultiple(ctx context.Context, data map[string]interface{}, expiration time.Duration) error {
	if len(data) == 0 {
		return nil
	}

	pipe := c.client.Pipeline()
	for key, value := range data {
		jsonData, err := json.Marshal(value)
		if err != nil {
			return err
		}
		pipe.Set(ctx, key, jsonData, expiration)
	}
	_, err := pipe.Exec(ctx)
	return err
}

func (c *CacheServiceImpl) Exists(ctx context.Context, key string) (bool, error) {
	count, err := c.client.Exists(ctx, key).Result()
	return count > 0, err
}

// 缓存键生成器
type CacheKeyGenerator struct{}

func NewCacheKeyGenerator() *CacheKeyGenerator {
	return &CacheKeyGenerator{}
}

func (g *CacheKeyGenerator) CouponListKey(condition map[string]interface{}) string {
	// 生成基于条件的缓存键
	return fmt.Sprintf("coupon:list:%x", hashCondition(condition))
}

func (g *CacheKeyGenerator) CouponDetailKey(id uint64) string {
	return fmt.Sprintf("coupon:detail:%d", id)
}

func (g *CacheKeyGenerator) BrandDetailKey(id uint64) string {
	return fmt.Sprintf("brand:detail:%d", id)
}

func (g *CacheKeyGenerator) CategoryDetailKey(id uint64) string {
	return fmt.Sprintf("category:detail:%d", id)
}

func (g *CacheKeyGenerator) BrandListKey(condition map[string]interface{}) string {
	return fmt.Sprintf("brand:list:%x", hashCondition(condition))
}

func (g *CacheKeyGenerator) CategoryListKey(condition map[string]interface{}) string {
	return fmt.Sprintf("category:list:%x", hashCondition(condition))
}

func (g *CacheKeyGenerator) DealListKey(condition map[string]interface{}) string {
	return fmt.Sprintf("deal:list:%x", hashCondition(condition))
}

func (g *CacheKeyGenerator) DealDetailKey(id uint64) string {
	return fmt.Sprintf("deal:detail:%d", id)
}

// 简单的哈希函数
func hashCondition(condition map[string]interface{}) uint32 {
	data, _ := json.Marshal(condition)
	return simpleHash(string(data))
}

func hashIDs(ids []uint64) uint32 {
	data, _ := json.Marshal(ids)
	return simpleHash(string(data))
}

func simpleHash(s string) uint32 {
	h := uint32(0)
	for _, c := range s {
		h = h*31 + uint32(c)
	}
	return h
}
