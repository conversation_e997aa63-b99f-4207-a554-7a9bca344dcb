package linkbuxlib

import (
	"context"
	"coupon-backend/infra/external_gateway/linkbuxlib/linkbuxvo"
	"coupon-backend/infra/utils"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"strings"
)

// min returns the smaller of two integers
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// BatchGetMerchants fetches all merchants from external API
func BatchGetMerchants(accountName string, accountConfig map[string]interface{}) ([]map[string]interface{}, error) {
	token := accountConfig["token"].(string)
	limit := accountConfig["limit"].(int)
	page := 1
	var createDataRows []map[string]interface{}
	uniqueMap := make(map[string]bool)
	for {
		resp, err := getMerchants(token, limit, page)
		if err != nil {
			return nil, fmt.Errorf("failed to get merchants page %d: %w", page, err)
		}
		for _, merchant := range resp.Data.List {
			uniqueKey := merchant.Mid + merchant.Mcid
			if _, exists := uniqueMap[uniqueKey]; !exists {
				trackingUrlSmart := merchant.TrackingUrlSmart
				if len(trackingUrlSmart) <= 0 {
					trackingUrlSmart = merchant.SiteUrl
				}
				category := "Others"
				if _, ok := CategoryNameMap[merchant.Categories]; ok {
					category = CategoryNameMap[merchant.Categories]
				}
				rowData := map[string]interface{}{}
				rowData["id"] = merchant.Mid
				rowData["unique_name"] = strings.ReplaceAll(strings.ToLower(merchant.Mcid+merchant.Mid), " ", "")
				rowData["name"] = merchant.MerchantName
				rowData["description"] = ""
				rowData["category"] = category
				rowData["site_url"] = utils.ExtractDomain(merchant.SiteUrl)

				merchantLogo := merchant.Logo
				if len(merchantLogo) <= 0 {
					merchantLogo = "https://logo.clearbit.com/" + utils.ExtractDomain(merchant.SiteUrl)
				}
				rowData["logo"] = merchantLogo
				rowData["origin_url"] = merchant.SiteUrl
				rowData["country_name"] = merchant.PrimaryRegion
				rowData["supported_countries"] = merchant.SupportRegion
				if merchant.MerchantStatus == "Online" {
					rowData["status"] = 1
				} else {
					rowData["status"] = 0
				}
				rowData["tracking_url"] = trackingUrlSmart
				createDataRows = append(createDataRows, rowData)
				uniqueMap[uniqueKey] = true
			}
		}
		if page >= resp.Data.TotalPage {
			break
		}
		page++
	}
	return createDataRows, nil
}

// BatchGetCoupons fetches all coupons from external API
func BatchGetCoupons(accountName string, accountConfig map[string]interface{}) ([]map[string]interface{}, error) {
	token := accountConfig["token"].(string)
	limit := accountConfig["limit"].(int)
	page := 1
	var createDataRows []map[string]interface{}
	uniqueMap := make(map[string]bool)

	for {
		resp, err := getCoupons(token, limit, page)
		if err != nil {
			return nil, err
		}

		for _, coupon := range resp.Data.Data {
			uniqueKey := coupon.CouponId + coupon.Mid
			if _, exists := uniqueMap[uniqueKey]; !exists {
				if len(coupon.CouponCode) <= 0 {
					continue
				}
				rowData := map[string]interface{}{}
				category := "Other"
				if _, ok := CategoryNameMap[coupon.Category]; ok {
					category = CategoryNameMap[coupon.Category]
				}
				rowData["id"] = coupon.CouponId
				rowData["site_url"] = utils.ExtractDomain(coupon.OriginUrl)
				rowData["code"] = coupon.CouponCode
				rowData["discount"] = coupon.Discount
				rowData["name"] = coupon.CouponName
				rowData["category"] = category
				rowData["description"] = coupon.Description
				rowData["start_date"] = coupon.BeginDate
				rowData["end_date"] = coupon.EndDate
				rowData["status"] = 1
				createDataRows = append(createDataRows, rowData)
			}
		}

		if page >= resp.Data.TotalPage {
			break
		}
		page++
	}

	return createDataRows, nil
}

// BatchGetDeals fetches all deals from external API
func BatchGetDeals(accountName string, accountConfig map[string]interface{}) ([]map[string]interface{}, error) {
	token := accountConfig["token"].(string)
	limit := accountConfig["limit"].(int)
	page := 1
	var createDataRows []map[string]interface{}
	uniqueMap := make(map[string]bool)

	for {
		resp, err := getDeals(token, limit, page)
		if err != nil {
			return nil, err
		}

		for _, deal := range resp.Data.Data {
			uniqueKey := deal.DealId + deal.Mid
			if _, exists := uniqueMap[uniqueKey]; !exists {
				if len(deal.TrackingUrl) <= 0 {
					continue
				}
				rowData := map[string]interface{}{}
				category := "Other"
				if _, ok := CategoryNameMap[deal.Category]; ok {
					category = CategoryNameMap[deal.Category]
				}
				rowData["id"] = deal.DealId
				rowData["site_url"] = utils.ExtractDomain(deal.OriginUrl)
				rowData["code"] = deal.DealCode
				rowData["discount"] = deal.Discount
				rowData["name"] = deal.DealName
				rowData["category"] = category
				rowData["img"] = deal.Img
				rowData["tracking_url"] = deal.TrackingUrl
				rowData["description"] = deal.Description
				rowData["start_date"] = deal.BeginDate
				rowData["end_date"] = deal.EndDate
				rowData["status"] = 1
				createDataRows = append(createDataRows, rowData)
			}
		}

		if page >= resp.Data.TotalPage {
			break
		}
		page++
	}

	return createDataRows, nil
}

// getMerchants gets merchants from API with pagination
func getMerchants(token string, limit int, page int) (*linkbuxvo.GetMerchantsResp, error) {
	ctx := context.Background()

	params := map[string]interface{}{
		"token": token,
		"limit": limit,
		"page":  page,
		"mod":   "medium",
		"op":    "monetization_api",
	}

	headers := map[string]string{}
	fullURL := host + apiGetMerchants
	resp, err := remoteInvokeWithUrl(ctx, fullURL, http.MethodGet, params, headers, nil)
	if err != nil {
		return nil, fmt.Errorf("HTTP request failed: %w", err)
	}

	merchantResp := new(linkbuxvo.GetMerchantsResp)
	if err := json.Unmarshal(resp, merchantResp); err != nil {
		log.Printf("[ERROR] JSON unmarshal failed: %v", err)
		log.Printf("[ERROR] Full response body: %s", string(resp))
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}
	return merchantResp, nil
}

// getCoupons gets coupons from API with pagination
func getCoupons(token string, limit int, page int) (*linkbuxvo.GetCouponsResp, error) {
	ctx := context.Background()

	params := map[string]interface{}{
		"token": token,
		"limit": limit,
		"page":  page,
		"mod":   "coupon",
		"op":    "coupons",
	}

	headers := map[string]string{}

	resp, err := remoteInvokeWithUrl(ctx, host+apiGetCoupons, http.MethodGet, params, headers, nil)
	if err != nil {
		return nil, err
	}

	couponResp := new(linkbuxvo.GetCouponsResp)
	if err := json.Unmarshal(resp, couponResp); err != nil {
		return nil, err
	}

	return couponResp, nil
}

// getDeals gets deals from API with pagination
func getDeals(token string, limit int, page int) (*linkbuxvo.GetDealsResp, error) {
	ctx := context.Background()

	params := map[string]interface{}{
		"token": token,
		"limit": limit,
		"page":  page,
		"mod":   "message",
		"op":    "deals",
	}

	headers := map[string]string{}

	resp, err := remoteInvokeWithUrl(ctx, host+apiGetDeals, http.MethodGet, params, headers, nil)
	if err != nil {
		log.Printf("[ERROR] HTTP request failed: %v", err)
		return nil, fmt.Errorf("HTTP request failed: %w", err)
	}

	log.Printf("[HTTP] Raw response body: %s", string(resp))

	dealResp := new(linkbuxvo.GetDealsResp)
	if err := json.Unmarshal(resp, dealResp); err != nil {
		log.Printf("[ERROR] JSON unmarshal failed: %v", err)
		log.Printf("[ERROR] Full response body: %s", string(resp))

		// Try to handle the case where data is returned as an array directly
		var alternativeResp struct {
			Status struct {
				Code int    `json:"code"`
				Msg  string `json:"msg"`
			} `json:"status"`
			Data []struct {
				DealId        string  `json:"deal_id"`
				Mid           string  `json:"mid"`
				Mcid          string  `json:"mcid"`
				MerchantName  string  `json:"merchant_name"`
				PrimaryRegion *string `json:"primary_region"`
				OfferType     string  `json:"offer_type"`
				Country       *string `json:"country"`
				DealName      string  `json:"deal_name"`
				DealCode      string  `json:"deal_code"`
				Discount      string  `json:"discount"`
				Description   string  `json:"description"`
				Img           string  `json:"img"`
				BeginDate     string  `json:"begin_date"`
				EndDate       string  `json:"end_date"`
				OriginUrl     string  `json:"origin_url"`
				TrackingUrl   string  `json:"tracking_url"`
				Category      string  `json:"category"`
			} `json:"data"`
		}

		if altErr := json.Unmarshal(resp, &alternativeResp); altErr == nil {
			log.Printf("[INFO] Successfully parsed as alternative format with %d deals", len(alternativeResp.Data))
			// Convert to standard format
			dealResp.Status = alternativeResp.Status
			dealResp.Data.TotalItems = len(alternativeResp.Data)
			dealResp.Data.TotalPage = 1
			dealResp.Data.Limit = len(alternativeResp.Data)
			dealResp.Data.Page = 1
			dealResp.Data.Data = alternativeResp.Data
			return dealResp, nil
		}

		return nil, fmt.Errorf("failed to unmarshal response in both formats: %w", err)
	}

	log.Printf("[HTTP] Successfully parsed response - Status: %+v, Data items: %d",
		dealResp.Status, len(dealResp.Data.Data))

	return dealResp, nil
}

func toMap(data interface{}) (map[string]interface{}, error) {
	bytes, err := json.Marshal(data)
	if err != nil {
		return nil, err
	}
	var result map[string]interface{}
	err = json.Unmarshal(bytes, &result)
	if err != nil {
		return nil, err
	}
	return result, nil
}
