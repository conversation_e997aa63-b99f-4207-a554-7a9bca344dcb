package linkbuxlib

import (
	"context"
	"fmt"
	"io"
	"log"
	"net/http"
	"net/url"
	"time"
)

const (
	// API endpoints
	apiGetMerchants = "/api.php"
	apiGetCoupons   = "/api.php"
	apiGetDeals     = "/api.php"

	// Base host
	host = "https://www.linkbux.com"

	// Request settings
	requestInterval = time.Millisecond * 300
	overtimeTime    = time.Second * 180
)

// remoteInvokeWithUrl sends HTTP request to external API
func remoteInvokeWithUrl(ctx context.Context, baseUrl string, method string, params map[string]interface{}, headers map[string]string, body io.Reader) (respBody []byte, err error) {
	log.Printf("[HTTP] Starting request - Method: %s, URL: %s", method, baseUrl)

	// Add delay to prevent rate limiting
	time.Sleep(requestInterval)

	client := &http.Client{
		Timeout: overtimeTime,
	}

	fullUrl := baseUrl
	if len(params) > 0 {
		urlValues := url.Values{}
		for key, value := range params {
			urlValues.Add(key, fmt.Sprintf("%v", value))
		}
		fullUrl = fmt.Sprintf("%s?%s", baseUrl, urlValues.Encode())
	}

	log.Printf("[HTTP] Full URL with params: %s", fullUrl)

	req, err := http.NewRequestWithContext(ctx, method, fullUrl, body)
	if err != nil {
		log.Printf("[ERROR] Failed to create HTTP request: %v", err)
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Set default headers
	req.Header.Set("Content-Type", "application/json")

	// Set custom headers
	for key, value := range headers {
		req.Header.Set(key, value)
	}

	log.Printf("[HTTP] Request headers: %+v", req.Header)

	resp, err := client.Do(req)
	if err != nil {
		log.Printf("[ERROR] HTTP request execution failed: %v", err)
		return respBody, fmt.Errorf("HTTP request failed: %w", err)
	}
	defer resp.Body.Close()

	log.Printf("[HTTP] Response status: %d %s", resp.StatusCode, resp.Status)
	log.Printf("[HTTP] Response headers: %+v", resp.Header)

	respBody, err = io.ReadAll(resp.Body)
	if err != nil {
		log.Printf("[ERROR] Failed to read response body: %v", err)
		return respBody, fmt.Errorf("failed to read response: %w", err)
	}

	log.Printf("[HTTP] Response body length: %d bytes", len(respBody))

	// Check for non-200 status codes
	if resp.StatusCode != http.StatusOK {
		log.Printf("[ERROR] Non-200 status code: %d, body: %s", resp.StatusCode, string(respBody))
		return respBody, fmt.Errorf("HTTP %d: %s", resp.StatusCode, string(respBody))
	}

	return respBody, nil
}

var CategoryNameMap = map[string]string{
	"Home & Garden":                        "Home & Electronics",
	"Others":                               "Others",
	"Health & Beauty":                      "Health & Beauty",
	"Clothing & Accessories":               "Fashion & Apparel",
	"Education":                            "Jobs & Education",
	"Auto":                                 "Travel & Transportation",
	"Gifts & Flowers":                      "Gifts & Events",
	"Computers & Electronics":              "Home & Electronics",
	"Fashion":                              "Fashion & Apparel",
	"Food & Drink":                         "Food & Drink",
	"Telecommunications":                   "Home & Electronics",
	"Accessories & Peripherals":            "Home & Electronics",
	"B2B":                                  "Business & Industrial",
	"Accessories & Services":               "Fashion & Apparel",
	"Online Services & Software":           "Home & Electronics",
	"Department stores":                    "Consumer Goods",
	"Finance & Insurance & Legal Services": "Business & Industrial",
	"Business & Career":                    "Business & Industrial",
	"Apparel":                              "Fashion & Apparel",
	"Sports & Fitness & outdoors":          "Sports & Fitness",
	"Internet Services":                    "Home & Electronics",
	"Games & Toys":                         "Toys & Games",
	"Online Gaming":                        "Toys & Games",
	"Toys & kids":                          "Toys & Games",
	"Dating & Romance":                     "People & Society",
	"Marketing":                            "Business & Industrial",
	"Shopping":                             "Consumer Goods",
	"Ecommerce":                            "Consumer Goods",
	"Art & Entertainment":                  "Arts & Entertainment",
	"Services":                             "Business & Industrial",
	"Kitchen & Dining":                     "Home & Electronics",
	"Books & Magazines":                    "Science & Reference",
	"Web Hosting":                          "Home & Electronics",
	"Loans":                                "Business & Industrial",
	"Household Essentials & Services":      "Home & Electronics",
	"Investment":                           "Business & Industrial",
	"Travel":                               "Travel & Transportation",
	"Credit Cards":                         "Business & Industrial",
	"Shoes":                                "Fashion & Apparel",
	"Banking & Trading":                    "Business & Industrial",
	"Loans & Financial Services":           "Business & Industrial",
	"Utilities":                            "Home & Electronics",
	"Jewelry":                              "Fashion & Apparel",
	"Cosmetics":                            "Health & Beauty",
	"Airlines":                             "Travel & Transportation",
	"Technology":                           "Home & Electronics",
	"Food":                                 "Food & Drink",
	"Tickets":                              "Gifts & Events",
	"Bags":                                 "Fashion & Apparel",
	"Hotels":                               "Travel & Transportation",
	"Car Rental":                           "Travel & Transportation",
	"Health Care":                          "Health & Beauty",
	"Furniture & Home Decor":               "Home & Electronics",
	"Software":                             "Home & Electronics",
	"Events":                               "Gifts & Events",
	"Auction & Used Goods":                 "Auctions & Classifieds",
	"Productivity Tools":                   "Home & Electronics",
	"Games":                                "Toys & Games",
	"Party Goods":                          "Gifts & Events",
	"Recreation":                           "Hobbies & Leisure",
	"Eco-Friendly":                         "Eco-Friendly Products",
	"Lifestyle":                            "People & Society",
	"Pet Supplies":                         "Pets & Animals",
	"Appliances & Electrical":              "Home & Electronics",
	"Collectibles & Hobbies":               "Hobbies & Leisure",
	"Cars":                                 "Travel & Transportation",
	"Pets":                                 "Pets & Animals",
	"APPs":                                 "Home & Electronics",
	"Baby Products":                        "Consumer Goods",
	"Office":                               "Business & Industrial",
	"Pet Services":                         "Pets & Animals",
	"Automobiles & Auto Services":          "Travel & Transportation",
	"Toys":                                 "Toys & Games",
	"Diet & Nutrition":                     "Health & Beauty",
	"Sports Clothing":                      "Sports & Fitness",
	"People & Society":                     "People & Society",
	"Accessories":                          "Fashion & Apparel",
	"Spa & Personal Grooming":              "Health & Beauty",
	"Adult":                                "People & Society",
	"Bath & Body":                          "Health & Beauty",
	"Home Appliances":                      "Home & Electronics",
	"Tax Services":                         "Business & Industrial",
	"Accommodations":                       "Travel & Transportation",
	"Gaming":                               "Toys & Games",
	"Equipment":                            "Sports & Fitness",
	"Tourism & Attractions":                "Travel & Transportation",
	"Children":                             "Toys & Games",
	"Electricals":                          "Home & Electronics",
	"DIY":                                  "Home & Electronics",
	"Gifts":                                "Gifts & Events",
	"Sports & Exercise Equipment":          "Sports & Fitness",
	"Exercise & Health":                    "Health & Beauty",
	"Computers":                            "Home & Electronics",
	"Pharmaceuticals":                      "Health & Beauty",
	"Electronics":                          "Home & Electronics",
	"Audio & Video":                        "Home & Electronics",
	"Tickets & Shows":                      "Gifts & Events",
	"Photography":                          "Hobbies & Leisure",
	"Tools & Supplies":                     "Home & Electronics",
	"Outdoors":                             "Sports & Fitness",
	"Legal Services":                       "Business & Industrial",
	"Home Improvement":                     "Home & Electronics",
	"Handbags":                             "Fashion & Apparel",
	"Languages":                            "Jobs & Education",
	"Multimedia":                           "Arts & Entertainment",
	"Swimwear":                             "Fashion & Apparel",
	"Gift cards":                           "Gifts & Events",
	"Music":                                "Arts & Entertainment",
	"Beauty":                               "Health & Beauty",
	"Educational":                          "Jobs & Education",
	"Online Courses":                       "Jobs & Education",
	"Transportation":                       "Travel & Transportation",
	"Baby Essentials":                      "Consumer Goods",
	"Sportswear":                           "Sports & Fitness",
	"Cooking":                              "Food & Drink",
	"Gadgets":                              "Home & Electronics",
	"Health":                               "Health & Beauty",
	"Education, Training & Recruitment":    "Jobs & Education",
	"Food & Gifts":                         "Gifts & Events",
	"Lingerie":                             "Fashion & Apparel",
	"Real Estate":                          "Business & Industrial",
	"Kids":                                 "Toys & Games",
}
