package linkbuxvo

// GetMerchantsResp example:
//
//	{
//	 "status": {
//	   "code": 0,
//	   "msg": "Success"
//	 },
//	 "data": {
//	   "total_mcid": "59010",
//	   "total_page": 60,
//	   "limit": 1000,
//	   "list": [
//	     {
//	       "mcid": "0800krankende",
//	       "mid": "4952",
//	       "merchant_name": "0800 Krankenkassen DE",
//	       "comm_rate": "EUR 21",
//	       "comm_detail": "Vertragsabschluss (Mitgliedschaft bei Krankenkass: EUR21.0 | Default: EUR21.0",
//	       "comm_detail_usd": {
//	         "comm": "USD23.5",
//	         "detail": "Vertragsabschluss (Mitgliedschaft bei Krankenkass: USD23.5 | Default: USD23.5"
//	       },
//	       "site_url": "http://www.0800-krankenkassen.de/",
//	       "logo": "https://cdn.sitesasset.com/affiliate-static/2022/02/11164456323113124620.png",
//	       "categories": "Finance & Insurance & Legal Services",
//	       "tags": "Insurance|Savings & Investments",
//	       "offer_type": "CPS",
//	       "network_partner": "",
//	       "avg_payment_cycle": "149",
//	       "avg_payout": "70.00%",
//	       "support_deeplink": "Y",
//	       "primary_region": "DE",
//	       "support_region": "DE",
//	       "merchant_status": "Online",
//	       "datetime": "1689855530",
//	       "relationship": "Rejected",
//	       "tracking_url": null,
//	       "tracking_url_short": "",
//	       "tracking_url_smart": "",
//	       "promotional_methods": {
//	         "(Internal Use Only) Partner Collaboration": "0",
//	         "App": "-",
//	         "Cashback": "1",
//	         "Comparison Shopping Service (CSS)": "-",
//	         "Content": "1",
//	         "Content - PREMIUM": "-",
//	         "Coupon/Deal": "1",
//	         "E-mail": "1",
//	         "Influencer": "-",
//	         "Influencer - PREMIUM": "-",
//	         "Loyalty": "-",
//	         "Media Buyer": "-",
//	         "Plug-In": "0",
//	         "Price Comparison": "-",
//	         "Social Media Group": "-",
//	         "Subnetwork": "0",
//	         "TM+ Bidding": "0",
//	         "Web Portal": "-"
//	       },
//	       "rd": "120.0"
//	     },
//	     {
//	       "mcid": "1000farmacieitaaa",
//	       "mid": "225543",
//	       "merchant_name": "1000Farmacie IT",
//	       "comm_rate": "7%",
//	       "comm_detail": "Default: 7.0%",
//	       "comm_detail_usd": "",
//	       "site_url": "https://www.1000farmacie.it/",
//	       "logo": "https://cdn.sitesasset.com/affiliate-static/2024/06/11171809587734990213.png",
//	       "categories": "Others",
//	       "tags": "",
//	       "offer_type": "CPS",
//	       "network_partner": "",
//	       "avg_payment_cycle": "86",
//	       "avg_payout": "6.99%",
//	       "support_deeplink": "Y",
//	       "primary_region": "IT",
//	       "support_region": "IT",
//	       "merchant_status": "Online",
//	       "datetime": "1730170226",
//	       "relationship": "Rejected",
//	       "tracking_url": null,
//	       "tracking_url_short": "",
//	       "tracking_url_smart": "",
//	       "promotional_methods": {
//	         "(Internal Use Only) Partner Collaboration": "0",
//	         "App": "-",
//	         "Cashback": "-",
//	         "Comparison Shopping Service (CSS)": "-",
//	         "Content": "-",
//	         "Content - PREMIUM": "-",
//	         "Coupon/Deal": "-",
//	         "E-mail": "-",
//	         "Influencer": "-",
//	         "Influencer - PREMIUM": "-",
//	         "Loyalty": "-",
//	         "Media Buyer": "-",
//	         "Plug-In": "0",
//	         "Price Comparison": "-",
//	         "Social Media Group": "-",
//	         "Subnetwork": "0",
//	         "TM+ Bidding": "0",
//	         "Web Portal": "-"
//	       },
//	       "rd": "30.0"
//	     }
//	   ]
//	 }
//	}
type GetMerchantsResp struct {
	Status struct {
		Code int    `json:"code"`
		Msg  string `json:"msg"`
	} `json:"status"`
	Data struct {
		TotalMcid string `json:"total_mcid"`
		TotalPage int    `json:"total_page"`
		Limit     int    `json:"limit"`
		List      []struct {
			Mcid               string      `json:"mcid"`
			Mid                string      `json:"mid"`
			MerchantName       string      `json:"merchant_name"`
			CommRate           string      `json:"comm_rate"`
			CommDetail         string      `json:"comm_detail"`
			CommDetailUsd      interface{} `json:"comm_detail_usd"`
			SiteUrl            string      `json:"site_url"`
			Logo               string      `json:"logo"`
			Categories         string      `json:"categories"`
			Tags               string      `json:"tags"`
			OfferType          string      `json:"offer_type"`
			NetworkPartner     string      `json:"network_partner"`
			AvgPaymentCycle    string      `json:"avg_payment_cycle"`
			AvgPayout          string      `json:"avg_payout"`
			SupportDeeplink    string      `json:"support_deeplink"`
			PrimaryRegion      string      `json:"primary_region"`
			SupportRegion      string      `json:"support_region"`
			MerchantStatus     string      `json:"merchant_status"`
			Datetime           *string     `json:"datetime"`
			Relationship       string      `json:"relationship"`
			TrackingUrl        *string     `json:"tracking_url"`
			TrackingUrlShort   string      `json:"tracking_url_short"`
			TrackingUrlSmart   string      `json:"tracking_url_smart"`
			PromotionalMethods struct {
				InternalUseOnlyPartnerCollaboration string `json:"(Internal Use Only) Partner Collaboration"`
				App                                 string `json:"App"`
				Cashback                            string `json:"Cashback"`
				ComparisonShoppingServiceCSS        string `json:"Comparison Shopping Service (CSS)"`
				Content                             string `json:"Content"`
				ContentPREMIUM                      string `json:"Content - PREMIUM"`
				CouponDeal                          string `json:"Coupon/Deal"`
				EMail                               string `json:"E-mail"`
				Influencer                          string `json:"Influencer"`
				InfluencerPREMIUM                   string `json:"Influencer - PREMIUM"`
				Loyalty                             string `json:"Loyalty"`
				MediaBuyer                          string `json:"Media Buyer"`
				PlugIn                              string `json:"Plug-In"`
				PriceComparison                     string `json:"Price Comparison"`
				SocialMediaGroup                    string `json:"Social Media Group"`
				Subnetwork                          string `json:"Subnetwork"`
				TMBidding                           string `json:"TM+ Bidding"`
				WebPortal                           string `json:"Web Portal"`
			} `json:"promotional_methods"`
			Rd string `json:"rd"`
		} `json:"list"`
	} `json:"data"`
}

// GetCouponsResp example:
//
//	{
//	 "status": {
//	   "code": 0,
//	   "msg": "Success"
//	 },
//	 "data": {
//	   "total_items": 37427,
//	   "total_page": 19,
//	   "limit": 2000,
//	   "page": 1,
//	   "data": [
//	     {
//	       "coupon_id": "1497408",
//	       "mid": "70915",
//	       "mcid": "straponmede",
//	       "merchant_name": "Strap on Me DE",
//	       "primary_region": "DE",
//	       "offer_type": "CPS",
//	       "coupon_name": "15 % Rabatt beim Kauf eines Strap-On-Produkts",
//	       "coupon_code": "KSTRAPON15",
//	       "discount": "15% Off",
//	       "description": "",
//	       "begin_date": "2025-07-14",
//	       "end_date": "",
//	       "origin_url": "https://strap-on-me.de/",
//	       "tracking_url": "https://www.linkbux.com/track/1462oUSzwOLcunzVH0fTN_bamzdFym6pZwEbGQenYIX4YIKPEg6uKHunOa4w2QFlAKhwsVFUYsA8_c?url=https%3A%2F%2Fstrap-on-me.de%2F",
//	       "logo": "https://cdn.sitesasset.com/affiliate-static/2023/03/02167772072800068309.png",
//	       "category": "Adult",
//	       "merchant_status": "Online",
//	       "relationship": "Joined"
//	     },
//	     {
//	       "coupon_id": "1497308",
//	       "mid": "263023",
//	       "mcid": "evvyaaa",
//	       "merchant_name": "Evvy",
//	       "primary_region": "US",
//	       "offer_type": "CPS",
//	       "coupon_name": "Get 10% Off",
//	       "coupon_code": "EVVY10OFF",
//	       "discount": "10% Off",
//	       "description": "",
//	       "begin_date": "2025-06-30",
//	       "end_date": "2050-12-31",
//	       "origin_url": "https://www.evvy.com",
//	       "tracking_url": "https://www.linkbux.com/track/8a75IHT5V9FLBFWM2Ky9MZNejxIMH2e1cp0V5jKrXajdIc9hzW8T9YnZhDU6SN7YSCx1mA_c_c?url=https%3A%2F%2Fwww.evvy.com",
//	       "logo": "https://cdn.sitesasset.com/affiliate-static/sas_logo/116251.jpg",
//	       "category": "Health & Beauty",
//	       "merchant_status": "Online",
//	       "relationship": "Joined"
//	     }
//	   ]
//	 }
//	}
type GetCouponsResp struct {
	Status struct {
		Code int    `json:"code"`
		Msg  string `json:"msg"`
	} `json:"status"`
	Data struct {
		TotalItems int `json:"total_items"`
		TotalPage  int `json:"total_page"`
		Limit      int `json:"limit"`
		Page       int `json:"page"`
		Data       []struct {
			CouponId       string  `json:"coupon_id"`
			Mid            string  `json:"mid"`
			Mcid           string  `json:"mcid"`
			MerchantName   string  `json:"merchant_name"`
			PrimaryRegion  *string `json:"primary_region"`
			OfferType      string  `json:"offer_type"`
			CouponName     string  `json:"coupon_name"`
			CouponCode     string  `json:"coupon_code"`
			Discount       string  `json:"discount"`
			Description    string  `json:"description"`
			BeginDate      string  `json:"begin_date"`
			EndDate        string  `json:"end_date"`
			OriginUrl      string  `json:"origin_url"`
			TrackingUrl    string  `json:"tracking_url"`
			Logo           string  `json:"logo"`
			Category       string  `json:"category"`
			MerchantStatus string  `json:"merchant_status"`
			Relationship   string  `json:"relationship"`
		} `json:"data"`
	} `json:"data"`
}

// GetDealsResp example:
//
//	{
//	 "status": {
//	   "code": 0,
//	   "msg": "success"
//	 },
//	 "data": {
//	   "total_items": 3987,
//	   "total_page": 2,
//	   "limit": 2000,
//	   "page": 1,
//	   "data": [
//	     {
//	       "deal_id": "69553",
//	       "mid": "21958",
//	       "mcid": "filorga",
//	       "merchant_name": "Filorga",
//	       "primary_region": "US",
//	       "offer_type": "CPS",
//	       "country": "US",
//	       "deal_name": "Filorga: Bastille Day Savings",
//	       "deal_code": "",
//	       "discount": "25% OFF Sitewide",
//	       "description": "Filorga has Bastille Day Savings, 25% OFF Sitewide. No code needed.Valid until: 07/14/2025 23:59 PST. ",
//	       "img": "https://img.gocashback.net/i25o/ad/screenshot/2025/07/14175246251147857.png",
//	       "begin_date": "2025-07-14",
//	       "end_date": "2025-07-15",
//	       "origin_url": "https://us.filorga.com",
//	       "tracking_url": "https://www.linkbux.com/track/7be64cww7sgn5tKLKraCa_bcL4v7IDSSVQasWX6J5eaYBuCbj9UwbwCCZTTbxRonccekQOg_c_c?url=https%3A%2F%2Fus.filorga.com%2F%3F",
//	       "category": "Clothing & Accessories"
//	     },
//	     {
//	       "deal_id": "69545",
//	       "mid": "218154",
//	       "mcid": "nortonaaa",
//	       "merchant_name": "Norton",
//	       "primary_region": "HK",
//	       "offer_type": "CPS",
//	       "country": "HK",
//	       "deal_name": "Norton - Asia Pacific: Norton AntiVirus Plus 3 Device with Genie Scam Protection First Year",
//	       "deal_code": "",
//	       "discount": "57% OFF",
//	       "description": "Norton - Asia Pacific has Norton AntiVirus Plus 3 Device with Genie Scam Protection First Year for A$39.99, originally A$91.99.No code needed.Offer may end soon.",
//	       "img": "https://img.gocashback.net/i25o/ad/screenshot/2025/07/14175246440251979.png",
//	       "begin_date": "2025-07-14",
//	       "end_date": "2030-07-14",
//	       "origin_url": "https://au.norton.com/promo/affiliate/nav-plus-three-devices",
//	       "tracking_url": "https://www.linkbux.com/track/eb0dUZ_aPzqO01ID_bmRXxYsuwQP_b2usI9jEHUH1hQBquNWv0M1u45gz5o_a9aGcFAfdnk_buEDv?url=https%3A%2F%2Fau.norton.com%2Fpromo%2Faffiliate%2Fnav-plus-three-devices",
//	       "category": "Others"
//	     }
//	   ]
//	 }
//	}
type GetDealsResp struct {
	Status struct {
		Code int    `json:"code"`
		Msg  string `json:"msg"`
	} `json:"status"`
	Data struct {
		TotalItems int `json:"total_items"`
		TotalPage  int `json:"total_page"`
		Limit      int `json:"limit"`
		Page       int `json:"page"`
		Data       []struct {
			DealId        string  `json:"deal_id"`
			Mid           string  `json:"mid"`
			Mcid          string  `json:"mcid"`
			MerchantName  string  `json:"merchant_name"`
			PrimaryRegion *string `json:"primary_region"`
			OfferType     string  `json:"offer_type"`
			Country       *string `json:"country"`
			DealName      string  `json:"deal_name"`
			DealCode      string  `json:"deal_code"`
			Discount      string  `json:"discount"`
			Description   string  `json:"description"`
			Img           string  `json:"img"`
			BeginDate     string  `json:"begin_date"`
			EndDate       string  `json:"end_date"`
			OriginUrl     string  `json:"origin_url"`
			TrackingUrl   string  `json:"tracking_url"`
			Category      string  `json:"category"`
		} `json:"data"`
	} `json:"data"`
}
