package fatcouponlib

import (
	"context"
	"fmt"
	"io"
	"log"
	"net/http"
	"net/url"
	"time"
)

const (
	// API endpoints
	apiGetMerchants = "/stores"

	// Base host
	host = "https://service.fatcoupon.com"

	// Request settings
	requestInterval = time.Millisecond * 300
	overtimeTime    = time.Second * 180
)

// remoteInvokeWithUrl sends HTTP request to external API
func remoteInvokeWithUrl(ctx context.Context, baseUrl string, method string, params map[string]interface{}, headers map[string]string, body io.Reader) (respBody []byte, err error) {
	log.Printf("[HTTP] Starting request - Method: %s, URL: %s", method, baseUrl)

	// Add delay to prevent rate limiting
	time.Sleep(requestInterval)

	client := &http.Client{
		Timeout: overtimeTime,
	}

	fullUrl := baseUrl
	if len(params) > 0 {
		urlValues := url.Values{}
		for key, value := range params {
			urlValues.Add(key, fmt.Sprintf("%v", value))
		}
		fullUrl = fmt.Sprintf("%s?%s", baseUrl, urlValues.Encode())
	}

	log.Printf("[HTTP] Full URL with params: %s", fullUrl)

	req, err := http.NewRequestWithContext(ctx, method, fullUrl, body)
	if err != nil {
		log.Printf("[ERROR] Failed to create HTTP request: %v", err)
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Set default headers
	req.Header.Set("Content-Type", "application/json")

	// Set custom headers
	for key, value := range headers {
		req.Header.Set(key, value)
	}

	log.Printf("[HTTP] Request headers: %+v", req.Header)

	resp, err := client.Do(req)
	if err != nil {
		log.Printf("[ERROR] HTTP request execution failed: %v", err)
		return respBody, fmt.Errorf("HTTP request failed: %w", err)
	}
	defer resp.Body.Close()

	log.Printf("[HTTP] Response status: %d %s", resp.StatusCode, resp.Status)
	log.Printf("[HTTP] Response headers: %+v", resp.Header)

	respBody, err = io.ReadAll(resp.Body)
	if err != nil {
		log.Printf("[ERROR] Failed to read response body: %v", err)
		return respBody, fmt.Errorf("failed to read response: %w", err)
	}

	log.Printf("[HTTP] Response body length: %d bytes", len(respBody))

	// Check for non-200 status codes
	if resp.StatusCode != http.StatusOK {
		log.Printf("[ERROR] Non-200 status code: %d, body: %s", resp.StatusCode, string(respBody))
		return respBody, fmt.Errorf("HTTP %d: %s", resp.StatusCode, string(respBody))
	}

	return respBody, nil
}

var CategoryNameMap = map[string]string{
	"sports-outdoors":  "Sports & Fitness",
	"home":             "Home & Electronics",
	"shoes":            "Fashion & Apparel",
	"electronics":      "Home & Electronics",
	"fashion-clothing": "Fashion & Apparel",
	"others":           "Others",
	"beauty":           "Health & Beauty",
	"men":              "Fashion & Apparel",
	"travel":           "Travel & Transportation",
	"food":             "Food & Drink",
	"kids-babies":      "Toys & Games",
	"handbags-wallets": "Fashion & Apparel",
	"health":           "Health & Beauty",
	"auto":             "Business & Industrial",
	"jewelry-watches":  "Fashion & Apparel",
	"holiday":          "Gifts & Events",
	"Others":           "Others",
}
