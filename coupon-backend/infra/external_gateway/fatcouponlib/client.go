package fatcouponlib

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"strings"

	"coupon-backend/infra/external_gateway/fatcouponlib/fatcouponvo"
	"coupon-backend/infra/utils"
)

// BatchGetMerchants fetches all merchants from external API
func BatchGetMerchants(accountName string, accountConfig map[string]interface{}) ([]map[string]interface{}, error) {
	token := accountConfig["token"].(string)
	limit := accountConfig["limit"].(int)
	page := 1
	var createDataRows []map[string]interface{}
	uniqueMap := make(map[string]bool)

	for {
		resp, err := getMerchants(token, limit, page)
		if err != nil {
			return nil, fmt.Errorf("failed to get merchants page %d: %w", page, err)
		}
		for _, merchant := range resp.Data.Data {
			// Skip if no cashback or commission rate is nil
			if merchant.IsNoCashback || merchant.CommissionRate.Rate == "" {
				continue
			}

			// Create unique key using id and slug
			uniqueKey := fmt.Sprintf("%s_%s", merchant.Id, merchant.Slug)
			if uniqueMap[uniqueKey] {
				continue
			}

			// Get category from first category slug
			category := "Others"
			if len(merchant.Categories) > 0 {
				categorySlug := merchant.Categories[0].Slug
				if mappedCategory, exists := CategoryNameMap[categorySlug]; exists {
					category = mappedCategory
				} else {
					category = CategoryNameMap["Others"]
				}
			} else {
				category = CategoryNameMap["Others"]
			}

			rowData := map[string]interface{}{}
			rowData["id"] = merchant.Id
			rowData["unique_name"] = strings.ReplaceAll(strings.ToLower(merchant.Slug+merchant.Id), " ", "")
			rowData["name"] = merchant.Name
			rowData["description"] = merchant.Description
			rowData["category"] = category
			rowData["site_url"] = utils.ExtractDomain(merchant.Domain)

			merchantLogo := merchant.Logo
			if len(merchantLogo) <= 0 {
				merchantLogo = "https://logo.clearbit.com/" + utils.ExtractDomain(merchant.Domain)
			}
			rowData["logo"] = merchantLogo
			rowData["origin_url"] = merchant.Domain
			rowData["country_name"] = merchant.Country
			rowData["supported_countries"] = merchant.Country
			rowData["status"] = 1 // Default to enabled
			trackUrl := fmt.Sprintf("https://link.fatcoupon.com/redirect?direct=false&url=%v&storeId=%v&partner=", merchant.Domain, merchant.Id)
			rowData["tracking_url"] = trackUrl

			createDataRows = append(createDataRows, rowData)
			uniqueMap[uniqueKey] = true
		}

		if page >= resp.Data.TotalPages {
			break
		}
		page++
	}

	return createDataRows, nil
}

// getMerchants gets merchants from API with pagination
func getMerchants(token string, limit int, page int) (*fatcouponvo.GetMerchantsResp, error) {
	ctx := context.Background()

	params := map[string]interface{}{
		"pageNum":  page,
		"pageSize": limit,
	}

	headers := map[string]string{
		"Cookie":     token,
		"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
	}

	fullURL := host + apiGetMerchants
	resp, err := remoteInvokeWithUrl(ctx, fullURL, http.MethodGet, params, headers, nil)
	if err != nil {
		log.Printf("[ERROR] HTTP request failed: %v", err)
		return nil, fmt.Errorf("HTTP request failed: %w", err)
	}

	merchantResp := new(fatcouponvo.GetMerchantsResp)
	if err := json.Unmarshal(resp, merchantResp); err != nil {
		log.Printf("[ERROR] JSON unmarshal failed: %v", err)
		log.Printf("[ERROR] Full response body: %s", string(resp))
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}

	log.Printf("[HTTP] Successfully parsed response - Errno: %d, Data items: %d",
		merchantResp.Errno, len(merchantResp.Data.Data))

	// Check for API error
	if merchantResp.Errno != 0 {
		return nil, fmt.Errorf("API error: %s (errno: %d)", merchantResp.Errmsg, merchantResp.Errno)
	}

	return merchantResp, nil
}

// min returns the minimum of two integers
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
