package utils

import (
	"bufio"
	"fmt"
	"log"
	"net/url"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"
)

// DomainMatcher 高性能域名匹配器
type DomainMatcher struct {
	domains    map[string]bool // 域名集合，O(1) 查找
	domainList []string        // 域名列表，用于调试
	loadTime   time.Time       // 加载时间
	mu         sync.RWMutex    // 读写锁
	urlCache   map[string]string // URL到域名的缓存
	cacheSize  int             // 缓存大小限制
}

var (
	globalDomainMatcher *DomainMatcher
	once                sync.Once
)

// GetDomainMatcher 获取全局域名匹配器实例（单例模式）
func GetDomainMatcher() *DomainMatcher {
	once.Do(func() {
		globalDomainMatcher = NewDomainMatcher()
		if err := globalDomainMatcher.LoadDomains(); err != nil {
			log.Printf("⚠️ Failed to load domains: %v", err)
			// 创建空的匹配器，避免程序崩溃
			globalDomainMatcher = &DomainMatcher{
				domains:   make(map[string]bool),
				urlCache:  make(map[string]string),
				cacheSize: 10000,
				loadTime:  time.Now(),
			}
		}
	})
	return globalDomainMatcher
}

// NewDomainMatcher 创建新的域名匹配器
func NewDomainMatcher() *DomainMatcher {
	return &DomainMatcher{
		domains:   make(map[string]bool),
		urlCache:  make(map[string]string),
		cacheSize: 10000, // 缓存最多10000个URL到域名的映射
	}
}

// LoadDomains 加载域名列表
func (dm *DomainMatcher) LoadDomains() error {
	start := time.Now()
	
	// 获取当前环境
	env := strings.ToLower(strings.TrimSpace(os.Getenv("ENV")))
	if env == "" {
		env = "live"
	}
	
	// 构建域名文件路径
	domainFile := filepath.Join("configs", env, "domain.txt")
	
	// 检查文件是否存在
	if _, err := os.Stat(domainFile); os.IsNotExist(err) {
		return fmt.Errorf("domain file not found: %s", domainFile)
	}
	
	// 打开文件
	file, err := os.Open(domainFile)
	if err != nil {
		return fmt.Errorf("failed to open domain file %s: %w", domainFile, err)
	}
	defer file.Close()
	
	dm.mu.Lock()
	defer dm.mu.Unlock()
	
	// 清空现有数据
	dm.domains = make(map[string]bool)
	dm.domainList = make([]string, 0)
	
	// 逐行读取域名
	scanner := bufio.NewScanner(file)
	lineCount := 0
	
	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())
		if line == "" || strings.HasPrefix(line, "#") {
			continue // 跳过空行和注释行
		}
		
		// 标准化域名（转小写，移除协议前缀）
		domain := normalizeDomain(line)
		if domain != "" {
			dm.domains[domain] = true
			dm.domainList = append(dm.domainList, domain)
			lineCount++
		}
	}
	
	if err := scanner.Err(); err != nil {
		return fmt.Errorf("error reading domain file: %w", err)
	}
	
	dm.loadTime = time.Now()
	duration := time.Since(start)
	
	log.Printf("🚀 Domain matcher loaded successfully:")
	log.Printf("   📁 File: %s", domainFile)
	log.Printf("   📊 Domains: %d", len(dm.domains))
	log.Printf("   ⏱️  Load time: %v", duration)
	log.Printf("   💾 Memory usage: ~%d KB", len(dm.domains)*50/1024) // 估算内存使用
	
	return nil
}

// IsFeaturedDomain 检查域名是否在特色域名列表中
func (dm *DomainMatcher) IsFeaturedDomain(siteURL string) bool {
	if siteURL == "" {
		return false
	}
	
	domain := dm.extractDomainWithCache(siteURL)
	if domain == "" {
		return false
	}
	
	dm.mu.RLock()
	defer dm.mu.RUnlock()
	
	return dm.domains[domain]
}

// extractDomainWithCache 从URL中提取域名（带缓存）
func (dm *DomainMatcher) extractDomainWithCache(siteURL string) string {
	// 先检查缓存
	dm.mu.RLock()
	if cached, exists := dm.urlCache[siteURL]; exists {
		dm.mu.RUnlock()
		return cached
	}
	dm.mu.RUnlock()
	
	// 提取域名
	domain := extractDomain(siteURL)
	
	// 写入缓存（如果缓存未满）
	dm.mu.Lock()
	if len(dm.urlCache) < dm.cacheSize {
		dm.urlCache[siteURL] = domain
	}
	dm.mu.Unlock()
	
	return domain
}

// extractDomain 从URL中提取域名
func extractDomain(siteURL string) string {
	if siteURL == "" {
		return ""
	}
	
	// 如果没有协议前缀，添加 http://
	if !strings.Contains(siteURL, "://") {
		siteURL = "http://" + siteURL
	}
	
	// 解析URL
	parsedURL, err := url.Parse(siteURL)
	if err != nil {
		// 如果解析失败，尝试直接处理
		return normalizeDomain(siteURL)
	}
	
	return normalizeDomain(parsedURL.Host)
}

// normalizeDomain 标准化域名
func normalizeDomain(domain string) string {
	if domain == "" {
		return ""
	}
	
	// 转小写
	domain = strings.ToLower(domain)
	
	// 移除协议前缀
	domain = strings.TrimPrefix(domain, "http://")
	domain = strings.TrimPrefix(domain, "https://")
	domain = strings.TrimPrefix(domain, "www.")
	
	// 移除端口号
	if colonIndex := strings.Index(domain, ":"); colonIndex != -1 {
		domain = domain[:colonIndex]
	}
	
	// 移除路径
	if slashIndex := strings.Index(domain, "/"); slashIndex != -1 {
		domain = domain[:slashIndex]
	}
	
	// 移除查询参数
	if questionIndex := strings.Index(domain, "?"); questionIndex != -1 {
		domain = domain[:questionIndex]
	}
	
	return strings.TrimSpace(domain)
}

// GetStats 获取域名匹配器统计信息
func (dm *DomainMatcher) GetStats() map[string]interface{} {
	dm.mu.RLock()
	defer dm.mu.RUnlock()
	
	return map[string]interface{}{
		"total_domains":  len(dm.domains),
		"cache_size":     len(dm.urlCache),
		"cache_limit":    dm.cacheSize,
		"load_time":      dm.loadTime.Format("2006-01-02 15:04:05"),
		"uptime_seconds": time.Since(dm.loadTime).Seconds(),
	}
}

// ReloadDomains 重新加载域名列表
func (dm *DomainMatcher) ReloadDomains() error {
	log.Println("🔄 Reloading domain matcher...")
	
	// 清空缓存
	dm.mu.Lock()
	dm.urlCache = make(map[string]string)
	dm.mu.Unlock()
	
	return dm.LoadDomains()
}

// BatchCheckDomains 批量检查域名（用于性能测试）
func (dm *DomainMatcher) BatchCheckDomains(urls []string) map[string]bool {
	result := make(map[string]bool, len(urls))
	
	for _, url := range urls {
		result[url] = dm.IsFeaturedDomain(url)
	}
	
	return result
}
