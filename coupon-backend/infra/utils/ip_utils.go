package utils

import (
	"net"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
)

// GetRealIP 获取用户真实IP地址
func GetRealIP(c *gin.Context) string {
	// 优先级顺序获取真实IP
	headers := []string{
		"CF-Connecting-IP",     // Cloudflare
		"True-Client-IP",       // Akamai and Cloudflare
		"X-Real-IP",           // Nginx proxy/FastCGI
		"X-Forwarded-For",     // Load balancers, proxies
		"X-Client-IP",         // Apache mod_proxy
		"X-Forwarded",         // Proxies
		"X-Cluster-Client-IP", // Cluster environments
		"Forwarded-For",       // RFC 7239
		"Forwarded",           // RFC 7239
	}

	// 1. 尝试从各种代理头部获取IP
	for _, header := range headers {
		ip := c.GetHeader(header)
		if ip != "" {
			// X-Forwarded-For 可能包含多个IP，取第一个
			if header == "X-Forwarded-For" {
				ips := strings.Split(ip, ",")
				if len(ips) > 0 {
					ip = strings.TrimSpace(ips[0])
				}
			}
			
			// 验证IP格式并且不是私有IP
			if isValidPublicIP(ip) {
				return ip
			}
		}
	}

	// 2. 使用Gin的ClientIP方法
	clientIP := c.ClientIP()
	if isValidPublicIP(clientIP) {
		return clientIP
	}

	// 3. 从RemoteAddr获取
	if c.Request != nil && c.Request.RemoteAddr != "" {
		host, _, err := net.SplitHostPort(c.Request.RemoteAddr)
		if err == nil && isValidPublicIP(host) {
			return host
		}
	}

	// 4. 开发环境处理：如果是本地IP，返回一个测试用的公网IP
	if isLocalIP(clientIP) {
		// 在开发环境中，可以通过环境变量设置测试IP
		// 或者使用一个默认的测试IP
		return getTestIP()
	}

	// 5. 最后返回原始IP（即使是本地IP）
	return clientIP
}

// isValidPublicIP 检查是否为有效的公网IP
func isValidPublicIP(ip string) bool {
	if ip == "" {
		return false
	}

	// 解析IP
	parsedIP := net.ParseIP(ip)
	if parsedIP == nil {
		return false
	}

	// 检查是否为公网IP（非私有、非回环、非链路本地）
	return !isLocalIP(ip)
}

// isLocalIP 检查是否为本地/私有IP
func isLocalIP(ip string) bool {
	if ip == "" || ip == "::1" || ip == "127.0.0.1" || ip == "localhost" {
		return true
	}

	parsedIP := net.ParseIP(ip)
	if parsedIP == nil {
		return true
	}

	// IPv4私有地址范围
	privateIPv4Ranges := []string{
		"10.0.0.0/8",     // Class A private
		"**********/12",  // Class B private
		"***********/16", // Class C private
		"***********/16", // Link-local
		"*********/8",    // Loopback
	}

	// IPv6私有地址范围
	privateIPv6Ranges := []string{
		"::1/128",        // Loopback
		"fc00::/7",       // Unique local
		"fe80::/10",      // Link-local
		"::ffff:0:0/96",  // IPv4-mapped IPv6
	}

	allRanges := append(privateIPv4Ranges, privateIPv6Ranges...)

	for _, cidr := range allRanges {
		_, network, err := net.ParseCIDR(cidr)
		if err != nil {
			continue
		}
		if network.Contains(parsedIP) {
			return true
		}
	}

	return false
}

// getTestIP 获取测试用的IP地址
func getTestIP() string {
	// 可以通过环境变量设置测试IP
	// testIP := os.Getenv("TEST_IP")
	// if testIP != "" {
	//     return testIP
	// }

	// 使用一些知名的公网IP作为测试
	testIPs := []string{
		"*******",         // Google DNS
		"*******",         // Cloudflare DNS
		"**************",  // OpenDNS
		"***************", // 114 DNS (China)
	}

	// 简单轮询返回不同的测试IP
	// 在实际应用中，您可能想要更智能的选择
	return testIPs[0] // 默认返回Google DNS IP
}

// GetIPFromRequest 从HTTP请求中获取IP（非Gin环境使用）
func GetIPFromRequest(r *http.Request) string {
	// 尝试从各种头部获取IP
	headers := []string{
		"CF-Connecting-IP",
		"True-Client-IP",
		"X-Real-IP",
		"X-Forwarded-For",
		"X-Client-IP",
	}

	for _, header := range headers {
		ip := r.Header.Get(header)
		if ip != "" {
			if header == "X-Forwarded-For" {
				ips := strings.Split(ip, ",")
				if len(ips) > 0 {
					ip = strings.TrimSpace(ips[0])
				}
			}
			
			if isValidPublicIP(ip) {
				return ip
			}
		}
	}

	// 从RemoteAddr获取
	if r.RemoteAddr != "" {
		host, _, err := net.SplitHostPort(r.RemoteAddr)
		if err == nil && isValidPublicIP(host) {
			return host
		}
	}

	// 如果是本地IP，返回测试IP
	if host, _, err := net.SplitHostPort(r.RemoteAddr); err == nil {
		if isLocalIP(host) {
			return getTestIP()
		}
		return host
	}

	return getTestIP()
}
