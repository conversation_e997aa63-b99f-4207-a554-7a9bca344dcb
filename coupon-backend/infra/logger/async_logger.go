package logger

import (
	"encoding/json"
	"fmt"
	"log"
	"os"
	"sync"
	"time"
)

// LogLevel 日志级别
type LogLevel int

const (
	DEBUG LogLevel = iota
	INFO
	WARN
	ERROR
	FATAL
)

func (l LogLevel) String() string {
	switch l {
	case DEBUG:
		return "DEBUG"
	case INFO:
		return "INFO"
	case WARN:
		return "WARN"
	case ERROR:
		return "ERROR"
	case FATAL:
		return "FATAL"
	default:
		return "UNKNOWN"
	}
}

// LogEntry 日志条目
type LogEntry struct {
	Timestamp time.Time              `json:"timestamp"`
	Level     LogLevel               `json:"level"`
	Message   string                 `json:"message"`
	Fields    map[string]interface{} `json:"fields,omitempty"`
	Source    string                 `json:"source,omitempty"`
}

// AsyncLogger 异步日志器
type AsyncLogger struct {
	logChan    chan LogEntry
	buffer     []LogEntry
	bufferSize int
	flushTime  time.Duration
	file       *os.File
	mu         sync.RWMutex
	closed     bool
	wg         sync.WaitGroup
}

// AsyncLoggerConfig 异步日志配置
type AsyncLoggerConfig struct {
	BufferSize    int           // 缓冲区大小
	FlushInterval time.Duration // 刷新间隔
	ChannelSize   int           // 通道大小
	LogFile       string        // 日志文件路径
	MinLevel      LogLevel      // 最小日志级别
}

// NewAsyncLogger 创建异步日志器
func NewAsyncLogger(config *AsyncLoggerConfig) (*AsyncLogger, error) {
	// 默认配置
	if config.BufferSize == 0 {
		config.BufferSize = 1000
	}
	if config.FlushInterval == 0 {
		config.FlushInterval = 1 * time.Second
	}
	if config.ChannelSize == 0 {
		config.ChannelSize = 10000
	}

	// 打开日志文件
	var file *os.File
	var err error
	if config.LogFile != "" {
		file, err = os.OpenFile(config.LogFile, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
		if err != nil {
			return nil, fmt.Errorf("failed to open log file: %v", err)
		}
	}

	logger := &AsyncLogger{
		logChan:    make(chan LogEntry, config.ChannelSize),
		buffer:     make([]LogEntry, 0, config.BufferSize),
		bufferSize: config.BufferSize,
		flushTime:  config.FlushInterval,
		file:       file,
	}

	// 启动后台写入协程
	logger.wg.Add(1)
	go logger.writeLoop()

	return logger, nil
}

// Log 记录日志（非阻塞）
func (l *AsyncLogger) Log(level LogLevel, message string, fields map[string]interface{}) {
	l.mu.RLock()
	if l.closed {
		l.mu.RUnlock()
		return
	}
	l.mu.RUnlock()

	entry := LogEntry{
		Timestamp: time.Now(),
		Level:     level,
		Message:   message,
		Fields:    fields,
	}

	select {
	case l.logChan <- entry:
		// 成功写入通道
	default:
		// 通道满时的处理策略：
		// 1. 丢弃日志（避免阻塞）- 当前策略
		// 2. 阻塞等待（可能影响性能）
		// 3. 写入备用缓冲区（增加内存使用）
		
		// 这里选择丢弃策略，因为性能优先
		// 在生产环境中可以通过监控丢弃的日志数量来调整配置
	}
}

// Debug 记录调试日志
func (l *AsyncLogger) Debug(message string, fields ...map[string]interface{}) {
	var f map[string]interface{}
	if len(fields) > 0 {
		f = fields[0]
	}
	l.Log(DEBUG, message, f)
}

// Info 记录信息日志
func (l *AsyncLogger) Info(message string, fields ...map[string]interface{}) {
	var f map[string]interface{}
	if len(fields) > 0 {
		f = fields[0]
	}
	l.Log(INFO, message, f)
}

// Warn 记录警告日志
func (l *AsyncLogger) Warn(message string, fields ...map[string]interface{}) {
	var f map[string]interface{}
	if len(fields) > 0 {
		f = fields[0]
	}
	l.Log(WARN, message, f)
}

// Error 记录错误日志
func (l *AsyncLogger) Error(message string, fields ...map[string]interface{}) {
	var f map[string]interface{}
	if len(fields) > 0 {
		f = fields[0]
	}
	l.Log(ERROR, message, f)
}

// Fatal 记录致命错误日志（同步写入，然后退出）
func (l *AsyncLogger) Fatal(message string, fields ...map[string]interface{}) {
	var f map[string]interface{}
	if len(fields) > 0 {
		f = fields[0]
	}
	
	// 致命错误需要立即写入
	entry := LogEntry{
		Timestamp: time.Now(),
		Level:     FATAL,
		Message:   message,
		Fields:    f,
	}
	
	l.writeEntry(entry)
	l.flush()
	
	// 退出程序
	os.Exit(1)
}

// writeLoop 后台写入循环
func (l *AsyncLogger) writeLoop() {
	defer l.wg.Done()
	
	ticker := time.NewTicker(l.flushTime)
	defer ticker.Stop()

	for {
		select {
		case entry, ok := <-l.logChan:
			if !ok {
				// 通道关闭，刷新剩余数据并退出
				l.flush()
				return
			}
			
			l.buffer = append(l.buffer, entry)
			
			// 缓冲区满时立即刷新
			if len(l.buffer) >= l.bufferSize {
				l.flush()
			}

		case <-ticker.C:
			// 定时刷新
			if len(l.buffer) > 0 {
				l.flush()
			}
		}
	}
}

// flush 刷新缓冲区
func (l *AsyncLogger) flush() {
	if len(l.buffer) == 0 {
		return
	}

	// 批量写入
	for _, entry := range l.buffer {
		l.writeEntry(entry)
	}

	// 清空缓冲区
	l.buffer = l.buffer[:0]
}

// writeEntry 写入单个日志条目
func (l *AsyncLogger) writeEntry(entry LogEntry) {
	// 格式化日志
	var output string
	if l.file != nil {
		// 文件输出使用JSON格式
		data, _ := json.Marshal(entry)
		output = string(data) + "\n"
	} else {
		// 控制台输出使用可读格式
		output = fmt.Sprintf("[%s] %s %s",
			entry.Timestamp.Format("2006-01-02 15:04:05"),
			entry.Level.String(),
			entry.Message,
		)
		
		if len(entry.Fields) > 0 {
			fieldsData, _ := json.Marshal(entry.Fields)
			output += " " + string(fieldsData)
		}
		output += "\n"
	}

	// 写入目标
	if l.file != nil {
		l.file.WriteString(output)
	} else {
		fmt.Print(output)
	}
}

// Close 关闭异步日志器
func (l *AsyncLogger) Close() error {
	l.mu.Lock()
	if l.closed {
		l.mu.Unlock()
		return nil
	}
	l.closed = true
	l.mu.Unlock()

	// 关闭通道
	close(l.logChan)
	
	// 等待写入完成
	l.wg.Wait()

	// 关闭文件
	if l.file != nil {
		return l.file.Close()
	}

	return nil
}

// GetStats 获取日志统计信息
func (l *AsyncLogger) GetStats() map[string]interface{} {
	l.mu.RLock()
	defer l.mu.RUnlock()
	
	return map[string]interface{}{
		"buffer_size":    len(l.buffer),
		"channel_size":   len(l.logChan),
		"max_buffer":     l.bufferSize,
		"max_channel":    cap(l.logChan),
		"closed":         l.closed,
	}
}

// 全局异步日志器实例
var globalAsyncLogger *AsyncLogger
var once sync.Once

// InitGlobalAsyncLogger 初始化全局异步日志器
func InitGlobalAsyncLogger(config *AsyncLoggerConfig) error {
	var err error
	once.Do(func() {
		globalAsyncLogger, err = NewAsyncLogger(config)
	})
	return err
}

// GetGlobalAsyncLogger 获取全局异步日志器
func GetGlobalAsyncLogger() *AsyncLogger {
	return globalAsyncLogger
}

// 便捷函数
func AsyncInfo(message string, fields ...map[string]interface{}) {
	if globalAsyncLogger != nil {
		globalAsyncLogger.Info(message, fields...)
	} else {
		log.Println("INFO:", message)
	}
}

func AsyncError(message string, fields ...map[string]interface{}) {
	if globalAsyncLogger != nil {
		globalAsyncLogger.Error(message, fields...)
	} else {
		log.Println("ERROR:", message)
	}
}

func AsyncWarn(message string, fields ...map[string]interface{}) {
	if globalAsyncLogger != nil {
		globalAsyncLogger.Warn(message, fields...)
	} else {
		log.Println("WARN:", message)
	}
}
