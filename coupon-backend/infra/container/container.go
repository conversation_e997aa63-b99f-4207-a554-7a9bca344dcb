package container

import (
	articleAppService "coupon-backend/application/article/appservice"
	brandAppService "coupon-backend/application/brand/appservice"
	categoryAppService "coupon-backend/application/category/appservice"
	couponAppService "coupon-backend/application/coupon/appservice"
	dealAppService "coupon-backend/application/deal/appservice"
	searchAppService "coupon-backend/application/search/appservice"
	"coupon-backend/application/subscription"
	"coupon-backend/config"
	articleRepository "coupon-backend/domain/article/repository"
	articleService "coupon-backend/domain/article/service"
	brandRepository "coupon-backend/domain/brand/repository"
	brandService "coupon-backend/domain/brand/service"
	categoryRepository "coupon-backend/domain/category/repository"
	categoryService "coupon-backend/domain/category/service"
	couponRepository "coupon-backend/domain/coupon/repository"
	couponService "coupon-backend/domain/coupon/service"
	dealRepository "coupon-backend/domain/deal/repository"
	dealService "coupon-backend/domain/deal/service"
	"coupon-backend/domain/subscription/repository"
	"coupon-backend/domain/subscription/service"
	"coupon-backend/infra/cache"
	"coupon-backend/infra/database"
	"coupon-backend/infra/geoip"
	"coupon-backend/infra/logger"
	"coupon-backend/infra/persistence"
	"coupon-backend/infra/pool"
	"coupon-backend/infra/warmup"
	"coupon-backend/interfaces/api/router"
	"log"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// Container 依赖注入容器
type Container struct {
	Config       *config.Config
	DB           *gorm.DB
	CacheService cache.CacheService
	GeoIPService *geoip.GeoIPService

	// Repositories
	ArticleRepo      articleRepository.ArticleRepository
	CategoryRepo     categoryRepository.CategoryRepository
	BrandRepo        brandRepository.BrandRepository
	CouponRepo       couponRepository.CouponRepository
	DealRepo         dealRepository.DealRepository
	SubscriptionRepo repository.SubscriptionRepo

	// Services
	ArticleService      articleService.ArticleService
	CategoryService     categoryService.CategoryService
	BrandService        brandService.BrandService
	CouponService       couponService.CouponService
	DealService         dealService.DealService
	SubscriptionService service.SubscriptionService

	// App Services
	ArticleAppService      articleAppService.ArticleAppService
	CategoryAppService     categoryAppService.CategoryAppService
	BrandAppService        brandAppService.BrandAppService
	CouponAppService       couponAppService.CouponAppService
	DealAppService         dealAppService.DealAppService
	SearchAppService       searchAppService.SearchAppService
	SubscriptionAppService *subscription.SubscriptionApp

	// Router
	Router *gin.Engine

	// Warmup Service
	WarmupService *warmup.WarmupService
}

// NewContainer 创建容器
func NewContainer() *Container {
	container := &Container{}
	container.initConfig()
	container.initAsyncLogger()
	container.initObjectPools()
	container.initDatabase()
	container.initCache()
	container.initGeoIP()
	container.initRepositories()
	container.initServices()
	container.initAppServices()
	container.initWarmupService()
	container.initRouter()
	return container
}

// initConfig 初始化配置
func (c *Container) initConfig() {
	c.Config = config.LoadConfig()
	log.Println("Config initialized")
}

// initAsyncLogger 初始化异步日志
func (c *Container) initAsyncLogger() {
	config := &logger.AsyncLoggerConfig{
		BufferSize:    1000,
		FlushInterval: 1 * time.Second,
		ChannelSize:   10000,
		LogFile:       "logs/app_async.log",
		MinLevel:      logger.INFO,
	}

	err := logger.InitGlobalAsyncLogger(config)
	if err != nil {
		log.Printf("Failed to initialize async logger: %v", err)
	} else {
		log.Println("Async logger initialized")
	}
}

// initObjectPools 初始化对象池
func (c *Container) initObjectPools() {
	pool.InitGlobalObjectPools()
	log.Println("Object pools initialized")
}

// initDatabase 初始化数据库
func (c *Container) initDatabase() {
	if err := persistence.InitDatabase(&c.Config.Database); err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}
	c.DB = persistence.GetDB()

	// 自动迁移
	if err := persistence.AutoMigrate(); err != nil {
		log.Fatalf("Failed to migrate database: %v", err)
	}

	// 初始化Redis
	if err := persistence.InitRedis(&c.Config.Redis); err != nil {
		log.Fatalf("Failed to initialize redis: %v", err)
	}

	// 初始化自适应连接池
	adaptiveConfig := &database.AdaptivePoolConfig{
		MinIdleConns:    5,
		MaxIdleConns:    20,
		MinOpenConns:    10,
		MaxOpenConns:    100,
		ConnMaxLifetime: 1 * time.Hour,
		AdjustInterval:  30 * time.Second,
		LoadThreshold:   0.8,
	}
	database.InitGlobalAdaptivePool(c.DB, adaptiveConfig)

	log.Println("Database, Redis and adaptive pool initialized")
}

// initCache 初始化缓存服务
func (c *Container) initCache() {
	// 使用已初始化的Redis客户端创建缓存服务
	redisClient := persistence.GetRedis()
	c.CacheService = cache.NewCacheService(redisClient)
	log.Println("Cache service initialized")
}

// initGeoIP 初始化IP地理位置服务
func (c *Container) initGeoIP() {
	c.GeoIPService = geoip.NewGeoIPService()
	log.Println("GeoIP service initialized")
}

// initRepositories 初始化仓储
func (c *Container) initRepositories() {
	c.ArticleRepo = persistence.NewArticleRepository(c.DB)
	c.CategoryRepo = persistence.NewCategoryRepository(c.DB)
	c.BrandRepo = persistence.NewBrandRepository(c.DB)
	c.CouponRepo = persistence.NewCouponRepository(c.DB)
	c.DealRepo = persistence.NewDealRepository(c.DB)
	c.SubscriptionRepo = persistence.NewSubscriptionRepo(c.DB)
	log.Println("Repositories initialized")
}

// initServices 初始化领域服务
func (c *Container) initServices() {
	c.ArticleService = articleService.NewArticleService(c.ArticleRepo)
	c.CategoryService = categoryService.NewCategoryService(c.CategoryRepo, c.CacheService)
	c.BrandService = brandService.NewBrandService(c.BrandRepo, c.CacheService)
	c.CouponService = couponService.NewCouponService(c.CouponRepo)
	c.DealService = dealService.NewDealService(c.DealRepo)
	c.SubscriptionService = service.NewSubscriptionService(c.SubscriptionRepo)
	log.Println("Domain services initialized")
}

// initAppServices 初始化应用服务
func (c *Container) initAppServices() {
	c.ArticleAppService = articleAppService.NewArticleAppService(c.ArticleService, c.BrandService, c.CategoryService, c.CouponService, c.DealService)
	c.CategoryAppService = categoryAppService.NewCategoryAppService(c.CategoryService)
	c.BrandAppService = brandAppService.NewBrandAppService(c.BrandService, c.CategoryService, c.CouponService, c.DealService)
	c.CouponAppService = couponAppService.NewCouponAppService(c.CouponService, c.BrandService, c.CategoryService, c.CacheService)
	c.DealAppService = dealAppService.NewDealAppService(c.DealService, c.BrandService, c.CategoryService)
	c.SearchAppService = searchAppService.NewSearchAppService(c.BrandAppService, c.CouponAppService, c.DealAppService)
	c.SubscriptionAppService = subscription.NewSubscriptionApp(c.SubscriptionService)
	log.Println("Application services initialized")
}

// initWarmupService 初始化预热服务
func (c *Container) initWarmupService() {
	c.WarmupService = warmup.NewWarmupService(c.CacheService)

	// 只执行启动预热（异步执行，不阻塞启动）
	go func() {
		c.WarmupService.StartupWarmup()
	}()

	log.Println("Warmup service initialized (startup warmup only)")
}

// initRouter 初始化路由
func (c *Container) initRouter() {
	routerConfig := &router.RouterConfig{
		ArticleAppService:      c.ArticleAppService,
		BrandAppService:        c.BrandAppService,
		CategoryAppService:     c.CategoryAppService,
		CouponAppService:       c.CouponAppService,
		DealAppService:         c.DealAppService,
		SearchAppService:       c.SearchAppService,
		SubscriptionAppService: c.SubscriptionAppService,
		CacheService:           c.CacheService,
	}
	c.Router = router.SetupRouter(routerConfig)
	log.Println("Router initialized")
}

// Cleanup cleans up resources
func (c *Container) Cleanup() {
	log.Println("🧹 Starting container cleanup...")

	// 1. Stop cache cleanup services
	log.Println("🔄 Stopping cache cleanup services...")
	if c.BrandService != nil {
		if brandServiceImpl, ok := c.BrandService.(*brandService.BrandServiceImpl); ok {
			brandServiceImpl.StopCacheCleanup()
		}
	}
	if c.CategoryService != nil {
		if categoryServiceImpl, ok := c.CategoryService.(*categoryService.CategoryServiceImpl); ok {
			categoryServiceImpl.StopCacheCleanup()
		}
	}
	log.Println("✅ Cache cleanup services stopped")

	// 2. Close adaptive connection pool
	log.Println("🔗 Closing adaptive connection pool...")
	if adaptivePool := database.GetGlobalAdaptivePool(); adaptivePool != nil {
		adaptivePool.Close()
		log.Println("✅ Adaptive connection pool closed")
	}

	// 3. Close async logger
	log.Println("📝 Closing async logger...")
	if asyncLogger := logger.GetGlobalAsyncLogger(); asyncLogger != nil {
		asyncLogger.Close()
		log.Println("✅ Async logger closed")
	}

	// 4. Close database connections
	log.Println("🗄️ Closing database connections...")
	if err := persistence.CloseDatabase(); err != nil {
		log.Printf("⚠️ Failed to close database: %v", err)
	} else {
		log.Println("✅ Database connections closed")
	}

	// 5. Close Redis connections
	log.Println("🔴 Closing Redis connections...")
	if err := persistence.CloseRedis(); err != nil {
		log.Printf("⚠️ Failed to close Redis: %v", err)
	} else {
		log.Println("✅ Redis connections closed")
	}

	log.Println("✅ Container cleanup completed")
}
