package constant

// 状态常量
const (
	// 通用状态
	StatusDisabled = 0 // 禁用
	StatusEnabled  = 1 // 启用

	// Brand状态
	BrandStatusDisabled = 0 // 下线
	BrandStatusEnabled  = 1 // 上线

	// Coupon状态
	CouponStatusDisabled = 0 // 禁用
	CouponStatusEnabled  = 1 // 启用

	// Deal状态
	DealStatusDisabled = 0 // 禁用
	DealStatusEnabled  = 1 // 启用
)

// 平台类型常量
const (
	PlatformTypeCJ      = "cj"      // Commission Junction
	PlatformTypeAwin    = "awin"    // Awin
	PlatformTypeLinkbux = "linkbux" // Linkbux
)

// 账户类型常量
const (
	AccountTypeLinkbux   = "linkbux"
	AccountTypeFatcoupon = "fatcoupon"
)

// 分页常量
const (
	DefaultPage     = 1
	DefaultPageSize = 20
	MaxPageSize     = 100
)

// 排序常量
const (
	SortByCreatedAt = "created_at"
	SortByUpdatedAt = "updated_at"
	SortByName      = "name"
	SortByFeatured  = "featured"
	SortOrderAsc    = "asc"
	SortOrderDesc   = "desc"
)

// 缓存键前缀
const (
	CacheKeyBrand    = "brand:"
	CacheKeyCategory = "category:"
	CacheKeyCoupon   = "coupon:"
	CacheKeyDeal     = "deal:"
	CacheKeySearch   = "search:"
)

// 缓存过期时间（秒）
const (
	CacheExpireShort  = 300   // 5分钟
	CacheExpireMedium = 1800  // 30分钟
	CacheExpireLong   = 3600  // 1小时
	CacheExpireDay    = 86400 // 24小时
)

// 外部平台账户配置
var MerchantsAccountList = []map[string]interface{}{
	{
		"account_name": "linkbux01",
		"type":         AccountTypeLinkbux,
		"token":        "mj2A8CXcblQZozBC",
		"limit":        1000,
		"priority":     3, // 优先级：数字越小优先级越高
	},
	{
		"account_name": "fatcoupon01",
		"type":         AccountTypeFatcoupon,
		"token":        "_fbp=fb.1.*************.*********; _hjSessionUser_3314300=********************************************************************************************************************; _pin_unauth=dWlkPU1UUTVPRFF3WXprdFlqSXlOeTAwT0RKaExUazRPREF0TURVeU1EZGpNbUV4TjJNeQ; fcrdi=2c16bce8-de10-494e-ac6a-793d3307aef5; _gid=GA1.2.**********.**********; _hjSession_3314300=****************************************************************************************************************************************************; _gcl_au=1.1.**********.**********.*********.**********.**********; token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************.2Cl1B0iMJi6564tmLbG3eN7QzV_l9jHWnQdIiF-Z9dc; _ga=GA1.2.**********.**********; acsFlag=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************.2Cl1B0iMJi6564tmLbG3eN7QzV_l9jHWnQdIiF-Z9dc; _uetsid=d12f386071c911f0b2aa8982ffefe7fa; isVerified=true; _ga_MM4V5V89ZL=GS2.1.s1754384919$o306$g1$t1754386129$j60$l0$h0",
		"limit":        1000,
		"priority":     1, // 优先级：数字越小优先级越高
	},
}

var CouponsAccountList = []map[string]interface{}{
	{
		"account_name": "linkbux01",
		"type":         AccountTypeLinkbux,
		"token":        "8c893ae41d201fc22c5e0b931f5c1c403c2fd143",
		"limit":        2000,
	},
}

var DealsAccountList = []map[string]interface{}{
	{
		"account_name": "linkbux01",
		"type":         AccountTypeLinkbux,
		"token":        "0aacf637793b06f27ca636e2f13f703c9c101f80",
		"limit":        2000,
	},
}
