package warmup

import (
	"context"
	"log"
	"sync"
	"time"

	"coupon-backend/infra/cache"
)

// WarmupService cache warmup service
type WarmupService struct {
	cacheService cache.CacheService
	keyGen       *cache.CacheKeyGenerator
}

// WarmupConfig warmup configuration
type WarmupConfig struct {
	MaxPages    int           // Warmup pages
	PageSize    int           // Page size
	CacheTTL    time.Duration // Cache TTL
	Concurrency int           // Concurrency
}

func NewWarmupService(cacheService cache.CacheService) *WarmupService {
	return &WarmupService{
		cacheService: cacheService,
		keyGen:       cache.NewCacheKeyGenerator(),
	}
}

// StartWarmup starts cache warmup
func (w *WarmupService) StartWarmup() {
	config := &WarmupConfig{
		MaxPages:    10,               // Warmup first 10 pages
		PageSize:    20,               // 20 items per page
		CacheTTL:    30 * time.Minute, // Cache for 30 minutes
		Concurrency: 4,                // 4 concurrent workers
	}

	log.Println("🔥 Starting cache warmup...")
	start := time.Now()

	var wg sync.WaitGroup
	semaphore := make(chan struct{}, config.Concurrency)

	// Warmup basic data
	wg.Add(1)
	go func() {
		defer wg.Done()
		semaphore <- struct{}{}
		defer func() { <-semaphore }()
		w.warmupBasicData(config)
	}()

	wg.Wait()
	duration := time.Since(start)
	log.Printf("✅ Cache warmup completed, duration: %v", duration)
}

// warmupBasicData warms up basic data
func (w *WarmupService) warmupBasicData(config *WarmupConfig) {
	// Warmup common cache keys
	basicKeys := []string{
		"system:status",
		"config:version",
		"stats:total_brands",
		"stats:total_categories",
		"stats:total_coupons",
		"stats:total_deals",
	}

	for _, key := range basicKeys {
		// Set basic cache data
		w.cacheService.Set(context.Background(), key, map[string]any{
			"timestamp": time.Now(),
			"status":    "active",
		}, config.CacheTTL)
	}

	log.Printf("✅ Basic data warmup completed")
}

// StartupWarmup startup cache warmup (execute only once)
func (w *WarmupService) StartupWarmup() {
	log.Println("🔥 Executing startup warmup...")
	w.StartWarmup()
	log.Println("✅ Startup warmup completed")
}

// WarmupSpecificData warms up specific data
func (w *WarmupService) WarmupSpecificData(dataType string, ids []uint64) {
	ctx := context.Background()

	switch dataType {
	case "brands":
		for _, id := range ids {
			key := w.keyGen.BrandDetailKey(id)
			// Here can fetch data from database and cache
			// To avoid circular dependency, only set placeholder here
			w.cacheService.Set(ctx, key, map[string]any{
				"id":        id,
				"prewarmed": true,
			}, 10*time.Minute)
		}
	case "categories":
		for _, id := range ids {
			key := w.keyGen.CategoryDetailKey(id)
			w.cacheService.Set(ctx, key, map[string]any{
				"id":        id,
				"prewarmed": true,
			}, 10*time.Minute)
		}
	case "coupons":
		for _, id := range ids {
			key := w.keyGen.CouponDetailKey(id)
			w.cacheService.Set(ctx, key, map[string]any{
				"id":        id,
				"prewarmed": true,
			}, 10*time.Minute)
		}
	}
}

// GetWarmupStats gets warmup statistics
func (w *WarmupService) GetWarmupStats() map[string]any {
	return map[string]any{
		"last_warmup": time.Now(),
		"status":      "active",
		"note":        "Simplified warmup service to avoid circular dependency",
	}
}
