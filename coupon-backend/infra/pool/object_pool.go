package pool

import (
	"sync"

	brandDto "coupon-backend/application/brand/dto"
	categoryDto "coupon-backend/application/category/dto"
	couponDto "coupon-backend/application/coupon/dto"
	dealDto "coupon-backend/application/deal/dto"
	searchDto "coupon-backend/application/search/dto"
)

// ObjectPools 对象池集合
type ObjectPools struct {
	// 响应对象池
	CouponListRespPool     *sync.Pool
	CouponDetailRespPool   *sync.Pool
	BrandListRespPool      *sync.Pool
	BrandDetailRespPool    *sync.Pool
	CategoryListRespPool   *sync.Pool
	CategoryDetailRespPool *sync.Pool
	DealListRespPool       *sync.Pool
	DealDetailRespPool     *sync.Pool
	SearchRespPool         *sync.Pool

	// 切片池
	CouponSlicePool   *sync.Pool
	BrandSlicePool    *sync.Pool
	CategorySlicePool *sync.Pool
	DealSlicePool     *sync.Pool
	Uint64SlicePool   *sync.Pool
	StringSlicePool   *sync.Pool

	// Map池
	CategoryMapPool *sync.Pool
	BrandMapPool    *sync.Pool
}

// NewObjectPools 创建对象池集合
func NewObjectPools() *ObjectPools {
	return &ObjectPools{
		// 响应对象池
		CouponListRespPool: &sync.Pool{
			New: func() interface{} {
				return &couponDto.CouponListResp{
					CouponList: make([]*couponDto.CouponDetailResp, 0, 20),
				}
			},
		},
		CouponDetailRespPool: &sync.Pool{
			New: func() interface{} {
				return &couponDto.CouponDetailResp{}
			},
		},
		BrandListRespPool: &sync.Pool{
			New: func() interface{} {
				return &brandDto.BrandListResp{
					BrandList: make([]*brandDto.BrandDetailResp, 0, 20),
				}
			},
		},
		BrandDetailRespPool: &sync.Pool{
			New: func() interface{} {
				return &brandDto.BrandDetailResp{}
			},
		},
		CategoryListRespPool: &sync.Pool{
			New: func() interface{} {
				return &categoryDto.CategoryListResp{
					CategoryList: make([]*categoryDto.CategoryDetailResp, 0, 20),
				}
			},
		},
		CategoryDetailRespPool: &sync.Pool{
			New: func() interface{} {
				return &categoryDto.CategoryDetailResp{}
			},
		},
		DealListRespPool: &sync.Pool{
			New: func() interface{} {
				return &dealDto.DealListResp{
					DealList: make([]*dealDto.DealDetailResp, 0, 20),
				}
			},
		},
		DealDetailRespPool: &sync.Pool{
			New: func() interface{} {
				return &dealDto.DealDetailResp{}
			},
		},
		SearchRespPool: &sync.Pool{
			New: func() interface{} {
				return &searchDto.SearchResponse{}
			},
		},

		// 切片池
		CouponSlicePool: &sync.Pool{
			New: func() interface{} {
				return make([]*couponDto.CouponDetailResp, 0, 20)
			},
		},
		BrandSlicePool: &sync.Pool{
			New: func() interface{} {
				return make([]*brandDto.BrandDetailResp, 0, 20)
			},
		},
		CategorySlicePool: &sync.Pool{
			New: func() interface{} {
				return make([]*categoryDto.CategoryDetailResp, 0, 20)
			},
		},
		DealSlicePool: &sync.Pool{
			New: func() interface{} {
				return make([]*dealDto.DealDetailResp, 0, 20)
			},
		},
		Uint64SlicePool: &sync.Pool{
			New: func() interface{} {
				return make([]uint64, 0, 50)
			},
		},
		StringSlicePool: &sync.Pool{
			New: func() interface{} {
				return make([]string, 0, 20)
			},
		},

		// Map池
		CategoryMapPool: &sync.Pool{
			New: func() interface{} {
				return make(map[uint64]*categoryDto.CategoryDetailResp, 20)
			},
		},
		BrandMapPool: &sync.Pool{
			New: func() interface{} {
				return make(map[uint64]*brandDto.BrandDetailResp, 20)
			},
		},
	}
}

// GetCouponListResp 获取优惠券列表响应对象
func (p *ObjectPools) GetCouponListResp() *couponDto.CouponListResp {
	return p.CouponListRespPool.Get().(*couponDto.CouponListResp)
}

// PutCouponListResp 归还优惠券列表响应对象
func (p *ObjectPools) PutCouponListResp(resp *couponDto.CouponListResp) {
	if resp != nil {
		// 手动重置对象状态
		resp.Total = 0
		resp.Page = 0
		resp.PageSize = 0
		resp.CouponList = resp.CouponList[:0] // 保留容量，只重置长度
		p.CouponListRespPool.Put(resp)
	}
}

// GetBrandListResp 获取商家列表响应对象
func (p *ObjectPools) GetBrandListResp() *brandDto.BrandListResp {
	return p.BrandListRespPool.Get().(*brandDto.BrandListResp)
}

// PutBrandListResp 归还商家列表响应对象
func (p *ObjectPools) PutBrandListResp(resp *brandDto.BrandListResp) {
	if resp != nil {
		// 简单重置，避免复杂的Reset方法
		resp.Total = 0
		resp.Page = 0
		resp.PageSize = 0
		if resp.BrandList != nil {
			resp.BrandList = resp.BrandList[:0]
		}
		p.BrandListRespPool.Put(resp)
	}
}

// GetCategoryListResp 获取分类列表响应对象
func (p *ObjectPools) GetCategoryListResp() *categoryDto.CategoryListResp {
	return p.CategoryListRespPool.Get().(*categoryDto.CategoryListResp)
}

// PutCategoryListResp 归还分类列表响应对象
func (p *ObjectPools) PutCategoryListResp(resp *categoryDto.CategoryListResp) {
	if resp != nil {
		resp.Total = 0
		resp.Page = 0
		resp.PageSize = 0
		if resp.CategoryList != nil {
			resp.CategoryList = resp.CategoryList[:0]
		}
		p.CategoryListRespPool.Put(resp)
	}
}

// GetDealListResp 获取活动列表响应对象
func (p *ObjectPools) GetDealListResp() *dealDto.DealListResp {
	return p.DealListRespPool.Get().(*dealDto.DealListResp)
}

// PutDealListResp 归还活动列表响应对象
func (p *ObjectPools) PutDealListResp(resp *dealDto.DealListResp) {
	if resp != nil {
		resp.Total = 0
		resp.Page = 0
		resp.PageSize = 0
		if resp.DealList != nil {
			resp.DealList = resp.DealList[:0]
		}
		p.DealListRespPool.Put(resp)
	}
}

// GetSearchResp 获取搜索响应对象
func (p *ObjectPools) GetSearchResp() *searchDto.SearchResponse {
	return p.SearchRespPool.Get().(*searchDto.SearchResponse)
}

// PutSearchResp 归还搜索响应对象
func (p *ObjectPools) PutSearchResp(resp *searchDto.SearchResponse) {
	if resp != nil {
		// 简单重置搜索响应
		resp.Total = 0
		resp.Brands = nil
		resp.Coupons = nil
		resp.Deals = nil
		p.SearchRespPool.Put(resp)
	}
}

// GetUint64Slice 获取uint64切片
func (p *ObjectPools) GetUint64Slice() []uint64 {
	return p.Uint64SlicePool.Get().([]uint64)
}

// PutUint64Slice 归还uint64切片
func (p *ObjectPools) PutUint64Slice(slice []uint64) {
	if slice != nil {
		// 重置切片但保留容量
		slice = slice[:0]
		p.Uint64SlicePool.Put(slice)
	}
}

// GetStringSlice 获取string切片
func (p *ObjectPools) GetStringSlice() []string {
	return p.StringSlicePool.Get().([]string)
}

// PutStringSlice 归还string切片
func (p *ObjectPools) PutStringSlice(slice []string) {
	if slice != nil {
		slice = slice[:0]
		p.StringSlicePool.Put(slice)
	}
}

// GetCategoryMap 获取分类Map
func (p *ObjectPools) GetCategoryMap() map[uint64]*categoryDto.CategoryDetailResp {
	return p.CategoryMapPool.Get().(map[uint64]*categoryDto.CategoryDetailResp)
}

// PutCategoryMap 归还分类Map
func (p *ObjectPools) PutCategoryMap(m map[uint64]*categoryDto.CategoryDetailResp) {
	if m != nil {
		// 清空map但保留容量
		for k := range m {
			delete(m, k)
		}
		p.CategoryMapPool.Put(m)
	}
}

// GetBrandMap 获取商家Map
func (p *ObjectPools) GetBrandMap() map[uint64]*brandDto.BrandDetailResp {
	return p.BrandMapPool.Get().(map[uint64]*brandDto.BrandDetailResp)
}

// PutBrandMap 归还商家Map
func (p *ObjectPools) PutBrandMap(m map[uint64]*brandDto.BrandDetailResp) {
	if m != nil {
		for k := range m {
			delete(m, k)
		}
		p.BrandMapPool.Put(m)
	}
}

// GetStats 获取对象池统计信息
func (p *ObjectPools) GetStats() map[string]interface{} {
	return map[string]interface{}{
		"pools_count": 14, // 当前池的数量
		"note":        "对象池统计信息需要自定义实现",
	}
}

// 全局对象池实例
var GlobalObjectPools *ObjectPools

// InitGlobalObjectPools 初始化全局对象池
func InitGlobalObjectPools() {
	GlobalObjectPools = NewObjectPools()
}

// GetGlobalObjectPools 获取全局对象池
func GetGlobalObjectPools() *ObjectPools {
	return GlobalObjectPools
}
