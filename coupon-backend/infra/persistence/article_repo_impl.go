package persistence

import (
	"coupon-backend/domain/article/entity"
	"coupon-backend/domain/article/repository"
	"coupon-backend/infra/ecode"
	"strings"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type ArticleRepositoryImpl struct {
	db *gorm.DB
}

func NewArticleRepository(db *gorm.DB) repository.ArticleRepository {
	return &ArticleRepositoryImpl{
		db: db,
	}
}

// GetArticleDetailById 根据ID获取文章详情
func (r *ArticleRepositoryImpl) GetArticleDetailById(ctx *gin.Context, id uint64) (*entity.Article, *ecode.Error) {
	var article entity.Article
	err := r.db.WithContext(ctx).Where("id = ?", id).First(&article).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, ecode.ErrNotFound
		}
		return nil, ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to get article by id")
	}
	return &article, nil
}

// GetArticleDetailBySlug 根据Slug获取文章详情
func (r *ArticleRepositoryImpl) GetArticleDetailBySlug(ctx *gin.Context, slug string) (*entity.Article, *ecode.Error) {
	var article entity.Article
	err := r.db.WithContext(ctx).Where("slug = ?", slug).First(&article).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, ecode.ErrNotFound
		}
		return nil, ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to get article by slug")
	}
	return &article, nil
}

// GetArticleListByCondition 根据条件获取文章列表
func (r *ArticleRepositoryImpl) GetArticleListByCondition(ctx *gin.Context, condition map[string]interface{}) ([]*entity.Article, int64, *ecode.Error) {
	var articles []*entity.Article
	var total int64

	query := r.db.WithContext(ctx).Model(&entity.Article{})

	// 发布状态过滤
	if published, ok := condition["published"].(bool); ok {
		query = query.Where("published = ?", published)
	}

	// 推荐状态过滤
	if featured, ok := condition["featured"].(bool); ok {
		query = query.Where("featured = ?", featured)
	}

	// 商家过滤
	if brandID, ok := condition["brand_id"].(uint64); ok && brandID > 0 {
		query = query.Where("brand_id = ?", brandID)
	}

	// 分类过滤
	if categoryID, ok := condition["category_id"].(uint64); ok && categoryID > 0 {
		query = query.Where("category_id = ?", categoryID)
	}

	// 搜索关键词
	if search, ok := condition["search"].(string); ok && search != "" {
		searchPattern := "%" + search + "%"
		query = query.Where("title LIKE ? OR description LIKE ? OR content LIKE ?", searchPattern, searchPattern, searchPattern)
	}

	// 获取总数
	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to count articles")
	}

	// 排序
	if sort, ok := condition["sort"].(string); ok && sort != "" {
		switch sort {
		case "created_at":
			query = query.Order("created_at DESC, id ASC")
		case "updated_at":
			query = query.Order("updated_at DESC, id ASC")
		case "published_at":
			query = query.Order("published_at DESC, id ASC")
		case "title":
			query = query.Order("title ASC, id ASC")
		case "featured":
			query = query.Order("featured DESC, created_at DESC, id ASC")
		default:
			query = query.Order("created_at DESC, id ASC")
		}
	} else {
		query = query.Order("created_at DESC, id ASC")
	}

	// 分页
	if offset, ok := condition["offset"].(int); ok && offset > 0 {
		query = query.Offset(offset)
	}
	if pageSize, ok := condition["page_size"].(int); ok && pageSize > 0 {
		query = query.Limit(pageSize)
	}

	err = query.Find(&articles).Error
	if err != nil {
		return nil, 0, ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to get articles")
	}

	return articles, total, nil
}

// GetArticleCount 获取文章总数
func (r *ArticleRepositoryImpl) GetArticleCount(ctx *gin.Context) (int64, *ecode.Error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&entity.Article{}).Count(&count).Error
	if err != nil {
		return 0, ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to count articles")
	}
	return count, nil
}

// GetArticleListByIDs 根据ID列表获取文章列表
func (r *ArticleRepositoryImpl) GetArticleListByIDs(ctx *gin.Context, ids []uint64) ([]*entity.Article, *ecode.Error) {
	if len(ids) == 0 {
		return []*entity.Article{}, nil
	}

	var articles []*entity.Article
	err := r.db.WithContext(ctx).Where("id IN ?", ids).Find(&articles).Error
	if err != nil {
		return nil, ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to get articles by ids")
	}
	return articles, nil
}

// CreateArticle 创建文章
func (r *ArticleRepositoryImpl) CreateArticle(ctx *gin.Context, article *entity.Article) *ecode.Error {
	err := r.db.WithContext(ctx).Create(article).Error
	if err != nil {
		if strings.Contains(err.Error(), "duplicate key") {
			return ecode.New(ecode.ErrInvalidParameter.Code, "article slug already exists")
		}
		return ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to create article")
	}
	return nil
}

// UpdateArticle 更新文章
func (r *ArticleRepositoryImpl) UpdateArticle(ctx *gin.Context, article *entity.Article) *ecode.Error {
	err := r.db.WithContext(ctx).Save(article).Error
	if err != nil {
		if strings.Contains(err.Error(), "duplicate key") {
			return ecode.New(ecode.ErrInvalidParameter.Code, "article slug already exists")
		}
		return ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to update article")
	}
	return nil
}

// DeleteArticle 删除文章
func (r *ArticleRepositoryImpl) DeleteArticle(ctx *gin.Context, id uint64) *ecode.Error {
	result := r.db.WithContext(ctx).Delete(&entity.Article{}, id)
	if result.Error != nil {
		return ecode.Wrap(result.Error, ecode.ErrDatabase.Code, "failed to delete article")
	}
	if result.RowsAffected == 0 {
		return ecode.ErrNotFound
	}
	return nil
}

// UpdateArticleStatus 更新文章发布状态
func (r *ArticleRepositoryImpl) UpdateArticleStatus(ctx *gin.Context, id uint64, published bool) *ecode.Error {
	result := r.db.WithContext(ctx).Model(&entity.Article{}).Where("id = ?", id).Update("published", published)
	if result.Error != nil {
		return ecode.Wrap(result.Error, ecode.ErrDatabase.Code, "failed to update article status")
	}
	if result.RowsAffected == 0 {
		return ecode.ErrNotFound
	}
	return nil
}
