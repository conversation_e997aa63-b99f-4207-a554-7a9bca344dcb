package persistence

import (
	"coupon-backend/domain/category/entity"
	"coupon-backend/domain/category/repository"
	"coupon-backend/infra/constant"
	"coupon-backend/infra/ecode"
	"fmt"
	"strings"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type CategoryRepositoryImpl struct {
	db *gorm.DB
}

// NewCategoryRepository 创建分类仓储实现
func NewCategoryRepository(db *gorm.DB) repository.CategoryRepository {
	return &CategoryRepositoryImpl{
		db: db,
	}
}

// GetCategoryDetailById 根据ID获取分类详情
func (r *CategoryRepositoryImpl) GetCategoryDetailById(ctx *gin.Context, id uint64) (*entity.Category, *ecode.Error) {
	var category entity.Category
	err := r.db.WithContext(ctx).Where("id = ?", id).First(&category).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, ecode.ErrNotFound
		}
		return nil, ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to get category by id")
	}
	return &category, nil
}

// GetCategoryDetailBySlug 根据Slug获取分类详情
func (r *CategoryRepositoryImpl) GetCategoryDetailBySlug(ctx *gin.Context, slug string) (*entity.Category, *ecode.Error) {
	var category entity.Category
	err := r.db.WithContext(ctx).Where("slug = ?", slug).First(&category).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, ecode.ErrNotFound
		}
		return nil, ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to get category by slug")
	}
	return &category, nil
}

// GetCategoryListByCondition 根据条件获取分类列表
func (r *CategoryRepositoryImpl) GetCategoryListByCondition(ctx *gin.Context, condition map[string]interface{}) ([]*entity.Category, int64, *ecode.Error) {
	var categories []*entity.Category
	var total int64

	query := r.db.WithContext(ctx).Model(&entity.Category{})

	// 搜索条件
	if search, ok := condition["search"].(string); ok && search != "" {
		searchPattern := "%" + search + "%"
		query = query.Where("name ILIKE ? OR slug ILIKE ?", searchPattern, searchPattern)
	}

	// 计算总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to count categories")
	}

	// 排序
	if sort, ok := condition["sort"].(string); ok && sort != "" {
		switch sort {
		case constant.SortByCreatedAt:
			query = query.Order("created_at DESC, id ASC")
		case constant.SortByUpdatedAt:
			query = query.Order("updated_at DESC, id ASC")
		case constant.SortByName:
			query = query.Order("name ASC, id ASC")
		default:
			query = query.Order("created_at DESC, id ASC")
		}
	} else {
		query = query.Order("created_at DESC, id ASC")
	}

	// 分页
	if pageSize, ok := condition["page_size"].(int); ok && pageSize > 0 {
		if pageSize > constant.MaxPageSize {
			pageSize = constant.MaxPageSize
		}
		query = query.Limit(pageSize)
	}

	if offset, ok := condition["offset"].(int); ok && offset >= 0 {
		query = query.Offset(offset)
	}

	// 执行查询
	if err := query.Find(&categories).Error; err != nil {
		return nil, 0, ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to get categories")
	}

	return categories, total, nil
}

// GetCategoryCount 获取分类总数
func (r *CategoryRepositoryImpl) GetCategoryCount(ctx *gin.Context) (int64, *ecode.Error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&entity.Category{}).Count(&count).Error
	if err != nil {
		return 0, ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to count categories")
	}
	return count, nil
}

// GetCategoryListByIDs 根据ID列表获取分类列表
func (r *CategoryRepositoryImpl) GetCategoryListByIDs(ctx *gin.Context, ids []uint64) ([]*entity.Category, *ecode.Error) {
	if len(ids) == 0 {
		return []*entity.Category{}, nil
	}

	var categories []*entity.Category
	err := r.db.WithContext(ctx).Where("id IN ?", ids).Find(&categories).Error
	if err != nil {
		return nil, ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to get categories by ids")
	}
	return categories, nil
}

// CreateCategory 创建分类
func (r *CategoryRepositoryImpl) CreateCategory(ctx *gin.Context, category *entity.Category) *ecode.Error {
	err := r.db.WithContext(ctx).Create(category).Error
	if err != nil {
		if strings.Contains(err.Error(), "duplicate key") {
			return ecode.New(ecode.ErrInvalidParameter.Code, "category slug already exists")
		}
		return ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to create category")
	}
	return nil
}

// UpdateCategory 更新分类
func (r *CategoryRepositoryImpl) UpdateCategory(ctx *gin.Context, category *entity.Category) *ecode.Error {
	err := r.db.WithContext(ctx).Save(category).Error
	if err != nil {
		if strings.Contains(err.Error(), "duplicate key") {
			return ecode.New(ecode.ErrInvalidParameter.Code, "category slug already exists")
		}
		return ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to update category")
	}
	return nil
}

// DeleteCategory 删除分类
func (r *CategoryRepositoryImpl) DeleteCategory(ctx *gin.Context, id uint64) *ecode.Error {
	result := r.db.WithContext(ctx).Delete(&entity.Category{}, id)
	if result.Error != nil {
		return ecode.Wrap(result.Error, ecode.ErrDatabase.Code, "failed to delete category")
	}
	if result.RowsAffected == 0 {
		return ecode.ErrNotFound
	}
	return nil
}

// BatchCreateCategories 批量创建分类
func (r *CategoryRepositoryImpl) BatchCreateCategories(ctx *gin.Context, categories []*entity.Category) *ecode.Error {
	if len(categories) == 0 {
		return nil
	}

	err := r.db.WithContext(ctx).CreateInBatches(categories, 100).Error
	if err != nil {
		return ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to batch create categories")
	}
	return nil
}

// BatchUpdateCategories 批量更新分类
func (r *CategoryRepositoryImpl) BatchUpdateCategories(ctx *gin.Context, categories []*entity.Category) *ecode.Error {
	if len(categories) == 0 {
		return nil
	}

	tx := r.db.WithContext(ctx).Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	for _, category := range categories {
		if err := tx.Save(category).Error; err != nil {
			tx.Rollback()
			return ecode.Wrap(err, ecode.ErrDatabase.Code, fmt.Sprintf("failed to update category %d", category.ID))
		}
	}

	if err := tx.Commit().Error; err != nil {
		return ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to commit batch update")
	}

	return nil
}
