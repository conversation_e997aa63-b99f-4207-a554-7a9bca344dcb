package persistence

import (
	"context"
	"coupon-backend/domain/subscription/entity"
	"coupon-backend/domain/subscription/repository"
	"coupon-backend/infra/ecode"

	"gorm.io/gorm"
)

type subscriptionRepoImpl struct {
	db *gorm.DB
}

// NewSubscriptionRepo 创建订阅仓储实现
func NewSubscriptionRepo(db *gorm.DB) repository.SubscriptionRepo {
	return &subscriptionRepoImpl{db: db}
}

// Create 创建订阅
func (r *subscriptionRepoImpl) Create(ctx context.Context, subscription *entity.Subscription) error {
	if err := r.db.WithContext(ctx).Create(subscription).Error; err != nil {
		return ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to create subscription")
	}
	return nil
}

// GetByEmail 根据邮箱获取订阅信息
func (r *subscriptionRepoImpl) GetByEmail(ctx context.Context, email string) (*entity.Subscription, error) {
	var subscription entity.Subscription
	err := r.db.WithContext(ctx).Where("email = ?", email).First(&subscription).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, ecode.Wrap(err, ecode.ErrNotFound.Code, "subscription not found")
		}
		return nil, ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to get subscription")
	}
	return &subscription, nil
}

// UpdateStatus 更新订阅状态
func (r *subscriptionRepoImpl) UpdateStatus(ctx context.Context, email string, status int) error {
	result := r.db.WithContext(ctx).Model(&entity.Subscription{}).
		Where("email = ?", email).
		Update("status", status)

	if result.Error != nil {
		return ecode.Wrap(result.Error, ecode.ErrDatabase.Code, "failed to update subscription status")
	}

	if result.RowsAffected == 0 {
		return ecode.New(ecode.ErrNotFound.Code, "subscription not found")
	}

	return nil
}

// List 获取订阅列表（分页）
func (r *subscriptionRepoImpl) List(ctx context.Context, condition map[string]interface{}) ([]*entity.Subscription, int64, error) {
	var subscriptions []*entity.Subscription
	var total int64

	query := r.db.WithContext(ctx).Model(&entity.Subscription{})

	// 应用筛选条件
	if status, ok := condition["status"].(int); ok {
		query = query.Where("status = ?", status)
	}

	if email, ok := condition["email"].(string); ok && email != "" {
		query = query.Where("email ILIKE ?", "%"+email+"%")
	}

	if source, ok := condition["source"].(string); ok && source != "" {
		query = query.Where("source = ?", source)
	}

	// 计算总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to count subscriptions")
	}

	// 分页
	page := 1
	pageSize := 20
	if p, ok := condition["page"].(int); ok && p > 0 {
		page = p
	}
	if ps, ok := condition["page_size"].(int); ok && ps > 0 && ps <= 100 {
		pageSize = ps
	}

	offset := (page - 1) * pageSize

	// 查询数据
	if err := query.Order("created_at DESC").Offset(offset).Limit(pageSize).Find(&subscriptions).Error; err != nil {
		return nil, 0, ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to list subscriptions")
	}

	return subscriptions, total, nil
}

// Delete 删除订阅（物理删除）
func (r *subscriptionRepoImpl) Delete(ctx context.Context, email string) error {
	result := r.db.WithContext(ctx).Where("email = ?", email).Delete(&entity.Subscription{})

	if result.Error != nil {
		return ecode.Wrap(result.Error, ecode.ErrDatabase.Code, "failed to delete subscription")
	}

	if result.RowsAffected == 0 {
		return ecode.New(ecode.ErrNotFound.Code, "subscription not found")
	}

	return nil
}
