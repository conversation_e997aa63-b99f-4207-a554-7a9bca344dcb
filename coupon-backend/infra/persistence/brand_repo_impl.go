package persistence

import (
	"coupon-backend/domain/brand/entity"
	"coupon-backend/domain/brand/repository"
	"coupon-backend/infra/constant"
	"coupon-backend/infra/ecode"
	"fmt"
	"strings"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type BrandRepositoryImpl struct {
	db *gorm.DB
}

// NewBrandRepository 创建商家仓储实现
func NewBrandRepository(db *gorm.DB) repository.BrandRepository {
	return &BrandRepositoryImpl{
		db: db,
	}
}

// GetBrandDetailById 根据ID获取商家详情
func (r *BrandRepositoryImpl) GetBrandDetailById(ctx *gin.Context, id uint64) (*entity.Brand, *ecode.Error) {
	var brand entity.Brand
	err := r.db.WithContext(ctx).Where("id = ?", id).First(&brand).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, ecode.ErrNotFound
		}
		return nil, ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to get brand by id")
	}
	return &brand, nil
}

// GetBrandDetailByUniqueName 根据UniqueName获取商家详情
func (r *BrandRepositoryImpl) GetBrandDetailByUniqueName(ctx *gin.Context, uniqueName string) (*entity.Brand, *ecode.Error) {
	var brand entity.Brand
	err := r.db.WithContext(ctx).Where("unique_name = ?", uniqueName).First(&brand).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, ecode.ErrNotFound
		}
		return nil, ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to get brand by unique name")
	}
	return &brand, nil
}

// GetBrandListByCondition 根据条件获取商家列表
func (r *BrandRepositoryImpl) GetBrandListByCondition(ctx *gin.Context, condition map[string]interface{}) ([]*entity.Brand, int64, *ecode.Error) {
	var brands []*entity.Brand
	var total int64

	query := r.db.WithContext(ctx).Model(&entity.Brand{})

	// 状态过滤
	if status, ok := condition["status"].(int); ok {
		query = query.Where("status = ?", status)
	}

	// 分类过滤
	if categoryID, ok := condition["category_id"].(uint64); ok && categoryID > 0 {
		query = query.Where("category_id = ?", categoryID)
	}

	// 推荐过滤
	if featured, ok := condition["featured"].(bool); ok && featured {
		query = query.Where("featured = ?", true)
	}

	// 搜索条件
	if search, ok := condition["search"].(string); ok && search != "" {
		searchPattern := "%" + search + "%"
		query = query.Where("name ILIKE ? OR unique_name ILIKE ? OR description ILIKE ?", searchPattern, searchPattern, searchPattern)
	}

	// 首字母过滤
	if startsWith, ok := condition["starts_with"].(string); ok && startsWith != "" {
		query = query.Where("name ILIKE ?", startsWith+"%")
	}

	// 计算总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to count brands")
	}

	// 排序
	if sort, ok := condition["sort"].(string); ok && sort != "" {
		switch sort {
		case constant.SortByCreatedAt:
			query = query.Order("created_at DESC, id ASC")
		case constant.SortByUpdatedAt:
			query = query.Order("updated_at DESC, id ASC")
		case constant.SortByName:
			query = query.Order("name ASC, id ASC")
		case constant.SortByFeatured:
			query = query.Order("featured DESC, created_at DESC, id ASC")
		default:
			query = query.Order("created_at DESC, id ASC")
		}
	} else {
		query = query.Order("created_at DESC, id ASC")
	}

	// 分页
	if pageSize, ok := condition["page_size"].(int); ok && pageSize > 0 {
		if pageSize > constant.MaxPageSize {
			pageSize = constant.MaxPageSize
		}
		query = query.Limit(pageSize)
	}

	if offset, ok := condition["offset"].(int); ok && offset >= 0 {
		query = query.Offset(offset)
	}

	// 执行查询
	if err := query.Find(&brands).Error; err != nil {
		return nil, 0, ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to get brands")
	}

	return brands, total, nil
}

// GetBrandCount 获取商家总数
func (r *BrandRepositoryImpl) GetBrandCount(ctx *gin.Context) (int64, *ecode.Error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&entity.Brand{}).Count(&count).Error
	if err != nil {
		return 0, ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to count brands")
	}
	return count, nil
}

// GetBrandListByIDs 根据ID列表获取商家列表
func (r *BrandRepositoryImpl) GetBrandListByIDs(ctx *gin.Context, ids []uint64) ([]*entity.Brand, *ecode.Error) {
	if len(ids) == 0 {
		return []*entity.Brand{}, nil
	}

	var brands []*entity.Brand
	err := r.db.WithContext(ctx).Where("id IN ?", ids).Find(&brands).Error
	if err != nil {
		return nil, ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to get brands by ids")
	}
	return brands, nil
}

// CreateBrand 创建商家
func (r *BrandRepositoryImpl) CreateBrand(ctx *gin.Context, brand *entity.Brand) *ecode.Error {
	err := r.db.WithContext(ctx).Create(brand).Error
	if err != nil {
		if strings.Contains(err.Error(), "duplicate key") {
			return ecode.New(ecode.ErrInvalidParameter.Code, "brand unique name already exists")
		}
		return ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to create brand")
	}
	return nil
}

// UpdateBrand 更新商家
func (r *BrandRepositoryImpl) UpdateBrand(ctx *gin.Context, brand *entity.Brand) *ecode.Error {
	err := r.db.WithContext(ctx).Save(brand).Error
	if err != nil {
		if strings.Contains(err.Error(), "duplicate key") {
			return ecode.New(ecode.ErrInvalidParameter.Code, "brand unique name already exists")
		}
		return ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to update brand")
	}
	return nil
}

// DeleteBrand 删除商家
func (r *BrandRepositoryImpl) DeleteBrand(ctx *gin.Context, id uint64) *ecode.Error {
	result := r.db.WithContext(ctx).Delete(&entity.Brand{}, id)
	if result.Error != nil {
		return ecode.Wrap(result.Error, ecode.ErrDatabase.Code, "failed to delete brand")
	}
	if result.RowsAffected == 0 {
		return ecode.ErrNotFound
	}
	return nil
}

// UpdateBrandStatus 更新商家状态
func (r *BrandRepositoryImpl) UpdateBrandStatus(ctx *gin.Context, id uint64, status int8) *ecode.Error {
	result := r.db.WithContext(ctx).Model(&entity.Brand{}).Where("id = ?", id).Update("status", status)
	if result.Error != nil {
		return ecode.Wrap(result.Error, ecode.ErrDatabase.Code, "failed to update brand status")
	}
	if result.RowsAffected == 0 {
		return ecode.ErrNotFound
	}
	return nil
}

// BatchCreateBrands 批量创建商家
func (r *BrandRepositoryImpl) BatchCreateBrands(ctx *gin.Context, brands []*entity.Brand) *ecode.Error {
	if len(brands) == 0 {
		return nil
	}

	err := r.db.WithContext(ctx).CreateInBatches(brands, 100).Error
	if err != nil {
		return ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to batch create brands")
	}
	return nil
}

// BatchUpdateBrands 批量更新商家
func (r *BrandRepositoryImpl) BatchUpdateBrands(ctx *gin.Context, brands []*entity.Brand) *ecode.Error {
	if len(brands) == 0 {
		return nil
	}

	tx := r.db.WithContext(ctx).Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	for _, brand := range brands {
		if err := tx.Save(brand).Error; err != nil {
			tx.Rollback()
			return ecode.Wrap(err, ecode.ErrDatabase.Code, fmt.Sprintf("failed to update brand %d", brand.ID))
		}
	}

	if err := tx.Commit().Error; err != nil {
		return ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to commit batch update")
	}

	return nil
}

// GetBrandsByPlatform 根据平台类型获取商家列表
func (r *BrandRepositoryImpl) GetBrandsByPlatform(ctx *gin.Context, platformType string) ([]*entity.Brand, *ecode.Error) {
	var brands []*entity.Brand
	err := r.db.WithContext(ctx).Where("platform_type = ?", platformType).Find(&brands).Error
	if err != nil {
		return nil, ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to get brands by platform")
	}
	return brands, nil
}

// FindBrandByPlatformAndMerchantID 根据平台类型和商家ID查找商家
func (r *BrandRepositoryImpl) FindBrandByPlatformAndMerchantID(ctx *gin.Context, platformType, merchantID string) (*entity.Brand, *ecode.Error) {
	var brand entity.Brand
	err := r.db.WithContext(ctx).Where("platform_type = ? AND platform_merchant_id = ?", platformType, merchantID).First(&brand).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, ecode.ErrNotFound
		}
		return nil, ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to find brand by platform and merchant id")
	}
	return &brand, nil
}

// GetAllBrands 获取所有商家
func (r *BrandRepositoryImpl) GetAllBrands(ctx *gin.Context) ([]*entity.Brand, *ecode.Error) {
	var brands []*entity.Brand
	err := r.db.WithContext(ctx).Find(&brands).Error
	if err != nil {
		return nil, ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to get all brands")
	}
	return brands, nil
}

// GetAllBrandsIncludingInactive 获取所有商家（包括已下线的）
func (r *BrandRepositoryImpl) GetAllBrandsIncludingInactive(ctx *gin.Context) ([]*entity.Brand, *ecode.Error) {
	var brands []*entity.Brand
	err := r.db.WithContext(ctx).Find(&brands).Error
	if err != nil {
		return nil, ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to get all brands including inactive")
	}
	return brands, nil
}

// FindBrandsBySiteURL 根据site_url查找所有商家
func (r *BrandRepositoryImpl) FindBrandsBySiteURL(ctx *gin.Context, siteURL string) ([]*entity.Brand, *ecode.Error) {
	var brands []*entity.Brand
	err := r.db.WithContext(ctx).Where("site_url = ?", siteURL).Find(&brands).Error
	if err != nil {
		return nil, ecode.Wrap(err, ecode.ErrDatabase.Code, "failed to find brands by site_url")
	}
	return brands, nil
}

// BatchUpdateBrandStatus 批量更新商家状态
func (r *BrandRepositoryImpl) BatchUpdateBrandStatus(ctx *gin.Context, brandIDs []uint, status int) *ecode.Error {
	if len(brandIDs) == 0 {
		return nil
	}

	result := r.db.WithContext(ctx).Model(&entity.Brand{}).Where("id IN ?", brandIDs).Update("status", status)
	if result.Error != nil {
		return ecode.Wrap(result.Error, ecode.ErrDatabase.Code, "failed to batch update brand status")
	}
	return nil
}

// UpdateBrandCouponCount 更新商家的优惠券数量
func (r *BrandRepositoryImpl) UpdateBrandCouponCount(ctx *gin.Context, brandID uint, count int) *ecode.Error {
	result := r.db.WithContext(ctx).Model(&entity.Brand{}).Where("id = ?", brandID).Update("total_coupons", count)
	if result.Error != nil {
		return ecode.Wrap(result.Error, ecode.ErrDatabase.Code, "failed to update brand coupon count")
	}
	if result.RowsAffected == 0 {
		return ecode.ErrNotFound
	}
	return nil
}

// UpdateBrandDealCount 更新商家的优惠活动数量
func (r *BrandRepositoryImpl) UpdateBrandDealCount(ctx *gin.Context, brandID uint, count int) *ecode.Error {
	result := r.db.WithContext(ctx).Model(&entity.Brand{}).Where("id = ?", brandID).Update("total_deals", count)
	if result.Error != nil {
		return ecode.Wrap(result.Error, ecode.ErrDatabase.Code, "failed to update brand deal count")
	}
	if result.RowsAffected == 0 {
		return ecode.ErrNotFound
	}
	return nil
}
