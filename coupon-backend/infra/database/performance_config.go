package database

import (
	"time"

	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// PerformanceConfig 数据库性能配置
type PerformanceConfig struct {
	MaxIdleConns    int           // 最大空闲连接数
	MaxOpenConns    int           // 最大打开连接数
	ConnMaxLifetime time.Duration // 连接最大生存时间
	ConnMaxIdleTime time.Duration // 连接最大空闲时间
	LogLevel        logger.LogLevel
}

// GetOptimizedConfig 获取优化的数据库配置
func GetOptimizedConfig(env string) *PerformanceConfig {
	switch env {
	case "local":
		return &PerformanceConfig{
			MaxIdleConns:    5,
			MaxOpenConns:    20,
			ConnMaxLifetime: 30 * time.Minute,
			ConnMaxIdleTime: 10 * time.Minute,
			LogLevel:        logger.Info,
		}
	case "live":
		return &PerformanceConfig{
			MaxIdleConns:    20,
			MaxOpenConns:    100,
			ConnMaxLifetime: 1 * time.Hour,
			ConnMaxIdleTime: 30 * time.Minute,
			LogLevel:        logger.Error, // 生产环境只记录错误
		}
	default:
		return &PerformanceConfig{
			MaxIdleConns:    10,
			MaxOpenConns:    50,
			ConnMaxLifetime: 45 * time.Minute,
			ConnMaxIdleTime: 15 * time.Minute,
			LogLevel:        logger.Warn,
		}
	}
}

// ApplyPerformanceConfig 应用性能配置到数据库连接
func ApplyPerformanceConfig(db *gorm.DB, config *PerformanceConfig) error {
	sqlDB, err := db.DB()
	if err != nil {
		return err
	}

	// 设置连接池参数
	sqlDB.SetMaxIdleConns(config.MaxIdleConns)
	sqlDB.SetMaxOpenConns(config.MaxOpenConns)
	sqlDB.SetConnMaxLifetime(config.ConnMaxLifetime)
	sqlDB.SetConnMaxIdleTime(config.ConnMaxIdleTime)

	return nil
}

// OptimizeQueries 查询优化配置
func OptimizeQueries(db *gorm.DB) *gorm.DB {
	return db.Session(&gorm.Session{
		PrepareStmt: true, // 预编译SQL语句
	})
}
