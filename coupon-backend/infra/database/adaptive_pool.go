package database

import (
	"log"
	"sync"
	"time"

	"gorm.io/gorm"
)

// AdaptivePoolConfig 自适应连接池配置
type AdaptivePoolConfig struct {
	MinIdleConns    int           // 最小空闲连接数
	MaxIdleConns    int           // 最大空闲连接数
	MinOpenConns    int           // 最小打开连接数
	MaxOpenConns    int           // 最大打开连接数
	ConnMaxLifetime time.Duration // 连接最大生命周期
	AdjustInterval  time.Duration // 调整间隔
	LoadThreshold   float64       // 负载阈值
}

// AdaptivePool 自适应连接池
type AdaptivePool struct {
	db     *gorm.DB
	config *AdaptivePoolConfig
	stats  *PoolStats
	mu     sync.RWMutex
	ticker *time.Ticker
	done   chan bool
}

// PoolStats 连接池统计
type PoolStats struct {
	OpenConnections int     // 当前打开连接数
	InUse          int     // 正在使用的连接数
	Idle           int     // 空闲连接数
	LoadFactor     float64 // 负载因子 (InUse / OpenConnections)
	WaitCount      int64   // 等待连接的次数
	WaitDuration   time.Duration // 平均等待时间
	LastAdjust     time.Time     // 上次调整时间
}

// NewAdaptivePool 创建自适应连接池
func NewAdaptivePool(db *gorm.DB, config *AdaptivePoolConfig) *AdaptivePool {
	// 设置默认值
	if config.MinIdleConns == 0 {
		config.MinIdleConns = 5
	}
	if config.MaxIdleConns == 0 {
		config.MaxIdleConns = 50
	}
	if config.MinOpenConns == 0 {
		config.MinOpenConns = 10
	}
	if config.MaxOpenConns == 0 {
		config.MaxOpenConns = 200
	}
	if config.ConnMaxLifetime == 0 {
		config.ConnMaxLifetime = 1 * time.Hour
	}
	if config.AdjustInterval == 0 {
		config.AdjustInterval = 30 * time.Second
	}
	if config.LoadThreshold == 0 {
		config.LoadThreshold = 0.8
	}

	pool := &AdaptivePool{
		db:     db,
		config: config,
		stats:  &PoolStats{},
		done:   make(chan bool),
	}

	// 应用初始配置
	pool.applyConfig()

	// 启动监控和调整
	pool.startMonitoring()

	return pool
}

// applyConfig 应用配置到数据库连接池
func (p *AdaptivePool) applyConfig() {
	sqlDB, err := p.db.DB()
	if err != nil {
		log.Printf("Failed to get sql.DB: %v", err)
		return
	}

	sqlDB.SetMaxIdleConns(p.config.MaxIdleConns)
	sqlDB.SetMaxOpenConns(p.config.MaxOpenConns)
	sqlDB.SetConnMaxLifetime(p.config.ConnMaxLifetime)

	log.Printf("Applied pool config: MaxIdle=%d, MaxOpen=%d, MaxLifetime=%v",
		p.config.MaxIdleConns, p.config.MaxOpenConns, p.config.ConnMaxLifetime)
}

// startMonitoring 启动监控和自动调整
func (p *AdaptivePool) startMonitoring() {
	p.ticker = time.NewTicker(p.config.AdjustInterval)
	
	go func() {
		for {
			select {
			case <-p.ticker.C:
				p.updateStats()
				p.adjustPool()
			case <-p.done:
				return
			}
		}
	}()
}

// updateStats 更新连接池统计信息
func (p *AdaptivePool) updateStats() {
	sqlDB, err := p.db.DB()
	if err != nil {
		return
	}

	stats := sqlDB.Stats()
	
	p.mu.Lock()
	defer p.mu.Unlock()
	
	p.stats.OpenConnections = stats.OpenConnections
	p.stats.InUse = stats.InUse
	p.stats.Idle = stats.Idle
	p.stats.WaitCount = stats.WaitCount
	p.stats.WaitDuration = stats.WaitDuration
	
	// 计算负载因子
	if p.stats.OpenConnections > 0 {
		p.stats.LoadFactor = float64(p.stats.InUse) / float64(p.stats.OpenConnections)
	} else {
		p.stats.LoadFactor = 0
	}
}

// adjustPool 根据负载情况调整连接池
func (p *AdaptivePool) adjustPool() {
	p.mu.Lock()
	defer p.mu.Unlock()
	
	oldMaxOpen := p.config.MaxOpenConns
	oldMaxIdle := p.config.MaxIdleConns
	
	// 根据负载因子调整
	if p.stats.LoadFactor > p.config.LoadThreshold {
		// 负载高，增加连接数
		if p.config.MaxOpenConns < 200 { // 设置上限
			p.config.MaxOpenConns = min(p.config.MaxOpenConns*2, 200)
			p.config.MaxIdleConns = min(p.config.MaxIdleConns*2, 50)
		}
	} else if p.stats.LoadFactor < 0.3 {
		// 负载低，减少连接数
		if p.config.MaxOpenConns > p.config.MinOpenConns {
			p.config.MaxOpenConns = max(p.config.MaxOpenConns/2, p.config.MinOpenConns)
			p.config.MaxIdleConns = max(p.config.MaxIdleConns/2, p.config.MinIdleConns)
		}
	}
	
	// 如果有等待，增加连接数
	if p.stats.WaitCount > 0 && p.stats.WaitDuration > 10*time.Millisecond {
		if p.config.MaxOpenConns < 200 {
			p.config.MaxOpenConns = min(p.config.MaxOpenConns+10, 200)
		}
	}
	
	// 应用新配置
	if oldMaxOpen != p.config.MaxOpenConns || oldMaxIdle != p.config.MaxIdleConns {
		p.applyConfig()
		p.stats.LastAdjust = time.Now()
		
		log.Printf("Pool adjusted: Load=%.2f, MaxOpen=%d->%d, MaxIdle=%d->%d, Wait=%d",
			p.stats.LoadFactor, oldMaxOpen, p.config.MaxOpenConns,
			oldMaxIdle, p.config.MaxIdleConns, p.stats.WaitCount)
	}
}

// GetStats 获取连接池统计信息
func (p *AdaptivePool) GetStats() *PoolStats {
	p.mu.RLock()
	defer p.mu.RUnlock()
	
	// 返回副本
	return &PoolStats{
		OpenConnections: p.stats.OpenConnections,
		InUse:          p.stats.InUse,
		Idle:           p.stats.Idle,
		LoadFactor:     p.stats.LoadFactor,
		WaitCount:      p.stats.WaitCount,
		WaitDuration:   p.stats.WaitDuration,
		LastAdjust:     p.stats.LastAdjust,
	}
}

// GetConfig 获取当前配置
func (p *AdaptivePool) GetConfig() *AdaptivePoolConfig {
	p.mu.RLock()
	defer p.mu.RUnlock()
	
	// 返回副本
	return &AdaptivePoolConfig{
		MinIdleConns:    p.config.MinIdleConns,
		MaxIdleConns:    p.config.MaxIdleConns,
		MinOpenConns:    p.config.MinOpenConns,
		MaxOpenConns:    p.config.MaxOpenConns,
		ConnMaxLifetime: p.config.ConnMaxLifetime,
		AdjustInterval:  p.config.AdjustInterval,
		LoadThreshold:   p.config.LoadThreshold,
	}
}

// Close 关闭自适应连接池
func (p *AdaptivePool) Close() {
	if p.ticker != nil {
		p.ticker.Stop()
	}
	close(p.done)
}

// 辅助函数
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

func max(a, b int) int {
	if a > b {
		return a
	}
	return b
}

// 全局自适应连接池实例
var globalAdaptivePool *AdaptivePool

// InitGlobalAdaptivePool 初始化全局自适应连接池
func InitGlobalAdaptivePool(db *gorm.DB, config *AdaptivePoolConfig) {
	globalAdaptivePool = NewAdaptivePool(db, config)
}

// GetGlobalAdaptivePool 获取全局自适应连接池
func GetGlobalAdaptivePool() *AdaptivePool {
	return globalAdaptivePool
}
